{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <!-- En-tête avec boutons d'action -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-edit text-warning me-2"></i>
            Modifier la Tâche
        </h2>
        <div class="btn-group">
            <a href="{{ url_for('task.tasks') }}" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-arrow-left me-1"></i> Retour
            </a>
            <button type="submit" form="task-form" class="btn btn-warning btn-sm">
                <i class="fas fa-save me-1"></i> Mettre à jour
            </button>
        </div>
    </div>

    <!-- Formulaire dans le style du tableau -->
    <div class="task-group mb-4">
        <div class="group-header">
            <div class="d-flex align-items-center">
                <div class="group-indicator bg-warning"></div>
                <h5 class="mb-0 me-3">Modification: {{ task.title }}</h5>
                <span class="badge bg-warning rounded-pill">Édition</span>
            </div>
        </div>
        
        <div class="group-content">
            <form method="POST" enctype="multipart/form-data" id="task-form">
                {{ form.hidden_tag() }}
                
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="20%">Champ</th>
                                <th width="60%">Valeur</th>
                                <th width="20%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Titre -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-heading me-2 text-primary"></i>
                                    {{ form.title.label.text }}
                                </td>
                                <td>
                                    {{ form.title(class="form-control form-control-sm") }}
                                    {% if form.title.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.title.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="text-muted">-</span>
                                </td>
                            </tr>
                            
                            <!-- Description -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-align-left me-2 text-info"></i>
                                    {{ form.description.label.text }}
                                </td>
                                <td>
                                    {{ form.description(class="form-control form-control-sm", rows="3") }}
                                    {% if form.description.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.description.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="text-muted">-</span>
                                </td>
                            </tr>
                            
                            <!-- Statut -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-flag me-2 text-warning"></i>
                                    {{ form.status.label.text }}
                                </td>
                                <td>
                                    {{ form.status(class="form-select form-select-sm", onchange="updateStatusPreview(this.value)") }}
                                    {% if form.status.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.status.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="status-badge status-{{ task.status }}" id="status-preview">
                                        {% if task.status == 'nouveau' %}Nouveau
                                        {% elif task.status == 'en_cours' %}En cours
                                        {% elif task.status == 'termine' %}Terminé
                                        {% elif task.status == 'annule' %}Bloqué
                                        {% endif %}
                                    </span>
                                </td>
                            </tr>
                            
                            <!-- Priorité -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                                    {{ form.priority.label.text }}
                                </td>
                                <td>
                                    {{ form.priority(class="form-select form-select-sm") }}
                                    {% if form.priority.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.priority.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge 
                                        {% if task.priority == 'basse' %}bg-secondary
                                        {% elif task.priority == 'normale' %}bg-info
                                        {% elif task.priority == 'haute' %}bg-warning
                                        {% elif task.priority == 'urgente' %}bg-danger
                                        {% endif %}">
                                        {{ task.priority|title }}
                                    </span>
                                </td>
                            </tr>
                            
                            <!-- Date d'échéance -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-calendar me-2 text-success"></i>
                                    {{ form.due_date.label.text }}
                                </td>
                                <td>
                                    {{ form.due_date(class="form-control form-control-sm", type="date") }}
                                    {% if form.due_date.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.due_date.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if task.due_date %}
                                    <span class="period-badge">
                                        {{ task.due_date.strftime('%d %b') }}
                                    </span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Catégorie -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-tag me-2 text-secondary"></i>
                                    {{ form.category.label.text }}
                                </td>
                                <td>
                                    {{ form.category(class="form-control form-control-sm") }}
                                    {% if form.category.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.category.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if task.category %}
                                    <span class="badge bg-light text-dark">{{ task.category }}</span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            
                            <!-- Assigné à -->
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-user me-2 text-info"></i>
                                    Assigné à
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-secondary text-white me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <select class="form-select form-select-sm" name="assigned_to">
                                            <option value="">Non assigné</option>
                                            {% for employee in employees %}
                                            <option value="{{ employee.id }}" {% if task.assigned_to == employee.id %}selected{% endif %}>
                                                {{ employee.nom }} {{ employee.prenom }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-muted">-</span>
                                </td>
                            </tr>
                            
                            <!-- Pièce jointe existante -->
                            {% if task.attachment_path %}
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-paperclip me-2 text-warning"></i>
                                    Pièce jointe actuelle
                                </td>
                                <td>
                                    <a href="{{ url_for('static', filename='uploads/' + task.attachment_path) }}" target="_blank" class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-download me-1"></i>
                                        {{ task.attachment_path.split('/')[-1] }}
                                    </a>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeAttachment()">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endif %}
                            
                            <!-- Nouvelle pièce jointe -->
                            {% if form.attachment %}
                            <tr>
                                <td class="fw-medium">
                                    <i class="fas fa-paperclip me-2 text-warning"></i>
                                    {{ form.attachment.label.text }}
                                </td>
                                <td>
                                    {{ form.attachment(class="form-control form-control-sm", type="file") }}
                                    {% if form.attachment.errors %}
                                        <div class="text-danger mt-1">
                                            {% for error in form.attachment.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="text-muted">Formats acceptés: PDF, DOC, DOCX, JPG, PNG (Max: 5MB)</small>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="previewFile()">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endif %}
                            
                            <!-- Actions de communication -->
                            <tr class="table-warning">
                                <td class="fw-medium">
                                    <i class="fas fa-share me-2 text-primary"></i>
                                    Communication
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-success" onclick="sendTaskWhatsApp()">
                                            <i class="fab fa-whatsapp me-1"></i> Envoyer par WhatsApp
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="sendTaskEmail()">
                                            <i class="fas fa-envelope me-1"></i> Envoyer par Email
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="generateTaskPDF()">
                                            <i class="fas fa-file-pdf me-1"></i> Générer PDF
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    <small class="text-muted">Partager la tâche mise à jour</small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>

    <!-- Historique des modifications -->
    <div class="task-group mb-4">
        <div class="group-header">
            <div class="d-flex align-items-center">
                <div class="group-indicator bg-info"></div>
                <h5 class="mb-0 me-3">Historique des Modifications</h5>
                <span class="badge bg-info rounded-pill">Suivi</span>
            </div>
        </div>
        
        <div class="group-content">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Date</th>
                            <th>Utilisateur</th>
                            <th>Modification</th>
                            <th>Ancienne valeur</th>
                            <th>Nouvelle valeur</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>{{ task.created_at.strftime('%d/%m/%Y %H:%M') if task.created_at else '-' }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-primary text-white me-2">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    Système
                                </div>
                            </td>
                            <td>Création de la tâche</td>
                            <td>-</td>
                            <td>{{ task.title }}</td>
                        </tr>
                        <!-- Ici on peut ajouter l'historique réel des modifications -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block styles %}
<style>
.task-group {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.group-header {
    background: #f8f9fa;
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
}

.group-indicator {
    width: 4px;
    height: 20px;
    border-radius: 2px;
    margin-right: 12px;
}

.group-content {
    background: white;
}

.avatar-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-nouveau {
    background: #e3f2fd;
    color: #1976d2;
}

.status-en_cours {
    background: #fff3e0;
    color: #f57c00;
}

.status-termine {
    background: #e8f5e8;
    color: #388e3c;
}

.status-annule {
    background: #ffebee;
    color: #d32f2f;
}

.period-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 13px;
    padding: 12px 8px;
}

.table td {
    padding: 12px 8px;
    vertical-align: middle;
    border-top: 1px solid #f1f3f4;
}

.form-control-sm, .form-select-sm {
    border-radius: 6px;
}

.btn-group .btn {
    border-radius: 6px;
    margin-left: 4px;
}

.btn-group .btn:first-child {
    margin-left: 0;
}

.table-warning {
    background-color: #fff3cd;
}
</style>
{% endblock %}

{% block scripts %}
<script>
function updateStatusPreview(status) {
    const statusElement = document.getElementById('status-preview');
    statusElement.className = `status-badge status-${status}`;
    
    const statusMap = {
        'nouveau': 'Nouveau',
        'en_cours': 'En cours',
        'termine': 'Terminé',
        'annule': 'Bloqué'
    };
    
    statusElement.textContent = statusMap[status] || 'Nouveau';
}

function sendWhatsApp(title) {
    const message = `Tâche mise à jour: ${title}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function sendEmail(title, description) {
    const subject = `Tâche mise à jour: ${title}`;
    const body = `Bonjour,\n\nLa tâche suivante a été mise à jour:\n\nTitre: ${title}\nDescription: ${description}\n\nCordialement`;
    
    const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoUrl;
}

function notifyAssignee(method) {
    const title = document.querySelector('input[name="title"]').value;
    
    if (method === 'whatsapp') {
        const message = `Vous avez été assigné à la tâche: ${title}`;
        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
        window.open(whatsappUrl, '_blank');
    } else if (method === 'email') {
        const subject = `Nouvelle assignation: ${title}`;
        const body = `Bonjour,\n\nVous avez été assigné à la tâche suivante:\n\nTitre: ${title}\n\nCordialement`;
        
        const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
        window.location.href = mailtoUrl;
    }
}

function sendTaskWhatsApp() {
    const title = document.querySelector('input[name="title"]').value;
    const description = document.querySelector('textarea[name="description"]').value;
    const status = document.querySelector('select[name="status"]').value;
    
    const message = `📋 Tâche: ${title}\n📝 Description: ${description}\n🏷️ Statut: ${status}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

function sendTaskEmail() {
    const title = document.querySelector('input[name="title"]').value;
    const description = document.querySelector('textarea[name="description"]').value;
    const status = document.querySelector('select[name="status"]').value;
    
    const subject = `Détails de la tâche: ${title}`;
    const body = `Bonjour,\n\nVoici les détails de la tâche:\n\nTitre: ${title}\nDescription: ${description}\nStatut: ${status}\n\nCordialement`;
    
    const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoUrl;
}

function generateTaskPDF() {
    // Rediriger vers la route d'impression
    window.open(`/task/print/{{ task.id }}`, '_blank');
}

function removeAttachment() {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette pièce jointe ?')) {
        // Ici on peut ajouter la logique pour supprimer la pièce jointe
        alert('Fonctionnalité en cours de développement');
    }
}

function previewFile() {
    const fileInput = document.querySelector('input[type="file"]');
    if (fileInput.files.length > 0) {
        const file = fileInput.files[0];
        alert(`Fichier sélectionné: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
    } else {
        alert('Aucun fichier sélectionné');
    }
}
</script>
{% endblock %}
