{% extends "base.html" %}

{% block title %}Modifier une tâche - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3"><i class="fas fa-edit me-2"></i>Modifier une tâche</h1>
        <p class="text-muted">Modifiez les détails de la tâche.</p>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Formulaire de modification de tâche</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('task.edit_task', id=task.id) }}">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">{{ form.title.label }}</label>
                        {{ form.title(class="form-control", placeholder="Titre de la tâche") }}
                        {% for error in form.title.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">{{ form.description.label }}</label>
                        {{ form.description(class="form-control", rows=5, placeholder="Description détaillée de la tâche") }}
                        {% for error in form.description.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{{ url_for('task.tasks') }}" class="btn btn-secondary me-md-2">Annuler</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
