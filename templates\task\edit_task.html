{% extends "base.html" %}

{% block title %}Modifier la tâche - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3"><i class="fas fa-edit me-2"></i>Modifier la tâche</h1>
        <p class="text-muted">Modifiez les informations de la tâche "{{ task.title }}".</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('task.tasks') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Retour à la liste
        </a>
        <a href="{{ url_for('task.view_task', id=task.id) }}" class="btn btn-info">
            <i class="fas fa-eye me-1"></i> Voir
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Informations de la tâche</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            {{ form.title.label(class="form-label") }}
                            {{ form.title(class="form-control") }}
                            {% if form.title.errors %}
                                <div class="text-danger">
                                    {% for error in form.title.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows="4") }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {% for error in form.description.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.status.label(class="form-label") }}
                            {{ form.status(class="form-select") }}
                            {% if form.status.errors %}
                                <div class="text-danger">
                                    {% for error in form.status.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.priority.label(class="form-label") }}
                            {{ form.priority(class="form-select") }}
                            {% if form.priority.errors %}
                                <div class="text-danger">
                                    {% for error in form.priority.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.due_date.label(class="form-label") }}
                            {{ form.due_date(class="form-control", type="date") }}
                            {% if form.due_date.errors %}
                                <div class="text-danger">
                                    {% for error in form.due_date.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.category.label(class="form-label") }}
                            {{ form.category(class="form-control") }}
                            {% if form.category.errors %}
                                <div class="text-danger">
                                    {% for error in form.category.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            {{ form.tags.label(class="form-label") }}
                            {{ form.tags(class="form-control") }}
                            <small class="form-text text-muted">Séparez les tags par des virgules (ex: urgent, important, projet)</small>
                            {% if form.tags.errors %}
                                <div class="text-danger">
                                    {% for error in form.tags.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if task.attachment_path %}
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label">Fichier actuel:</label>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-paperclip me-2"></i>
                                <a href="{{ url_for('task.download_file', filename=task.attachment_path.split('/')[-1]) }}" class="me-3">
                                    {{ task.attachment_path.split('/')[-1] }}
                                </a>
                                <small class="text-muted">(Téléchargé le {{ task.created_at.strftime('%d/%m/%Y') if task.created_at else 'Date inconnue' }})</small>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if form.attachment %}
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            {{ form.attachment.label(class="form-label") }}
                            {% if task.attachment_path %}
                                <small class="text-muted d-block mb-2">Sélectionnez un nouveau fichier pour remplacer l'actuel</small>
                            {% endif %}
                            {{ form.attachment(class="form-control") }}
                            <small class="form-text text-muted">Formats acceptés: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG (Max: 10MB)</small>
                            {% if form.attachment.errors %}
                                <div class="text-danger">
                                    {% for error in form.attachment.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Enregistrer les modifications
                            </button>
                            <a href="{{ url_for('task.tasks') }}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-1"></i> Annuler
                            </a>
                            <a href="{{ url_for('task.view_task', id=task.id) }}" class="btn btn-info ms-2">
                                <i class="fas fa-eye me-1"></i> Voir la tâche
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Informations de la tâche -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>ID de la tâche:</strong><br>
                    <span class="text-muted">#{{ task.id }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Créée le:</strong><br>
                    <span class="text-muted">{{ task.created_at.strftime('%d/%m/%Y à %H:%M') if task.created_at else 'Date inconnue' }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Dernière modification:</strong><br>
                    <span class="text-muted">{{ task.updated_at.strftime('%d/%m/%Y à %H:%M') if task.updated_at else 'Jamais modifiée' }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Statut actuel:</strong><br>
                    {% if task.status == 'new' %}
                        <span class="badge bg-secondary">Nouvelle</span>
                    {% elif task.status == 'in_progress' %}
                        <span class="badge bg-warning">En cours</span>
                    {% elif task.status == 'completed' %}
                        <span class="badge bg-success">Terminée</span>
                    {% elif task.status == 'cancelled' %}
                        <span class="badge bg-danger">Annulée</span>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <strong>Priorité actuelle:</strong><br>
                    {% if task.priority == 'low' %}
                        <span class="badge bg-light text-dark">Faible</span>
                    {% elif task.priority == 'normal' %}
                        <span class="badge bg-info">Normale</span>
                    {% elif task.priority == 'high' %}
                        <span class="badge bg-warning">Élevée</span>
                    {% elif task.priority == 'urgent' %}
                        <span class="badge bg-danger">Urgente</span>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Actions rapides -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>Actions rapides</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if task.status != 'completed' %}
                    <button type="button" class="btn btn-success btn-sm" onclick="markAsCompleted()">
                        <i class="fas fa-check me-1"></i> Marquer comme terminée
                    </button>
                    {% endif %}
                    
                    {% if task.status != 'in_progress' and task.status != 'completed' %}
                    <button type="button" class="btn btn-warning btn-sm" onclick="markAsInProgress()">
                        <i class="fas fa-play me-1"></i> Marquer en cours
                    </button>
                    {% endif %}
                    
                    <a href="{{ url_for('task.print_task', id=task.id) }}" class="btn btn-secondary btn-sm" target="_blank">
                        <i class="fas fa-print me-1"></i> Imprimer
                    </a>
                    
                    <button type="button" class="btn btn-danger btn-sm" onclick="confirmDelete()">
                        <i class="fas fa-trash me-1"></i> Supprimer
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la tâche <strong>"{{ task.title }}"</strong> ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <a href="{{ url_for('task.delete_task', id=task.id) }}" class="btn btn-danger">Supprimer</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Actions rapides
function markAsCompleted() {
    document.getElementById('status').value = 'completed';
    document.querySelector('form').submit();
}

function markAsInProgress() {
    document.getElementById('status').value = 'in_progress';
    document.querySelector('form').submit();
}

function confirmDelete() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Validation côté client
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const titleInput = document.getElementById('title');
    
    form.addEventListener('submit', function(e) {
        if (titleInput.value.trim() === '') {
            e.preventDefault();
            titleInput.focus();
            titleInput.classList.add('is-invalid');
            
            titleInput.addEventListener('input', function() {
                titleInput.classList.remove('is-invalid');
            });
        }
    });
});
</script>
{% endblock %}
