#!/usr/bin/env python3
"""
Test simple du système
"""

import sqlite3
import os

def test_database():
    """Tester la base de données"""
    
    print("🔧 Test de la base de données...")
    
    db_path = 'instance/app.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Base de données non trouvée: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test 1: Vérifier les tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = ['users', 'tasks', 'employees']
        missing_tables = [table for table in required_tables if table not in tables]
        
        if missing_tables:
            print(f"❌ Tables manquantes: {missing_tables}")
        else:
            print("✅ Toutes les tables requises sont présentes")
        
        # Test 2: Vérifier l'utilisateur admin
        cursor.execute("SELECT id, username, is_admin FROM users WHERE username = 'admin'")
        admin = cursor.fetchone()
        
        if admin:
            print(f"✅ Utilisateur admin trouvé: ID={admin[0]}, Admin={admin[2]}")
        else:
            print("❌ Utilisateur admin non trouvé")
        
        # Test 3: Vérifier les tâches
        cursor.execute("SELECT COUNT(*) FROM tasks")
        task_count = cursor.fetchone()[0]
        print(f"ℹ️ Nombre de tâches: {task_count}")
        
        if task_count > 0:
            cursor.execute("SELECT id, title, status FROM tasks LIMIT 3")
            tasks = cursor.fetchall()
            print("📋 Exemples de tâches:")
            for task in tasks:
                print(f"  - ID: {task[0]}, Titre: {task[1]}, Statut: {task[2]}")
        
        # Test 4: Vérifier la colonne assigned_to
        cursor.execute("PRAGMA table_info(tasks)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'assigned_to' in columns:
            print("✅ Colonne 'assigned_to' présente dans la table tasks")
        else:
            print("❌ Colonne 'assigned_to' manquante dans la table tasks")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test de la base de données: {e}")
        return False

def test_templates():
    """Tester les templates"""
    
    print("\n🔧 Test des templates...")
    
    required_templates = [
        'templates/task/tasks.html',
        'templates/task/add_task.html',
        'templates/task/edit_task.html',
        'templates/task/print_task.html'
    ]
    
    missing_templates = []
    for template in required_templates:
        if not os.path.exists(template):
            missing_templates.append(template)
    
    if missing_templates:
        print(f"❌ Templates manquants: {missing_templates}")
        return False
    else:
        print("✅ Tous les templates requis sont présents")
        return True

def test_routes():
    """Tester les routes dans le code"""
    
    print("\n🔧 Test des routes...")
    
    if not os.path.exists('routes.py'):
        print("❌ Fichier routes.py non trouvé")
        return False
    
    with open('routes.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_routes = [
        '@task_bp.route(\'/tasks\')',
        '@task_bp.route(\'/task/add\'',
        '@task_bp.route(\'/task/edit/<int:id>\'',
        '@task_bp.route(\'/task/delete/<int:id>\'',
        '@task_bp.route(\'/task/print/<int:id>\')'
    ]
    
    missing_routes = []
    for route in required_routes:
        if route not in content:
            missing_routes.append(route)
    
    if missing_routes:
        print(f"❌ Routes manquantes: {missing_routes}")
        return False
    else:
        print("✅ Toutes les routes requises sont présentes")
        return True

def main():
    """Test principal"""
    
    print("🚀 Test complet du système de gestion des tâches")
    print("=" * 50)
    
    tests = [
        ("Base de données", test_database),
        ("Templates", test_templates),
        ("Routes", test_routes)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans le test {test_name}: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 RÉSULTATS DES TESTS")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 TOUS LES TESTS SONT RÉUSSIS!")
        print("🌐 Le système devrait fonctionner correctement")
        print("🔗 Accédez à: http://localhost:5000/login")
        print("👤 Connexion: admin / admin")
    else:
        print("⚠️ CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    return all_passed

if __name__ == "__main__":
    main()
