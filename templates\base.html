<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gestion des Pointages{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Font Awesome Brands (pour WhatsApp) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/brands.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    {% block styles %}{% endblock %}
</head>
<body>
    {% if current_user.is_authenticated %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.dashboard') }}">
                <i class="fas fa-clipboard-check me-2"></i>Gestion des Pointages
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <!-- Tableau de bord -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'main.dashboard' %}active{% endif %}" href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i> Tableau de bord
                        </a>
                    </li>

                    <!-- Gestion des employés -->
                    {% if current_user.can_manage_employees or current_user.is_admin %}
                    <li class="nav-item">
                        <a class="nav-link {% if 'employee' in request.endpoint %}active{% endif %}" href="{{ url_for('employee.employees') }}">
                            <i class="fas fa-users me-1"></i> Employés
                        </a>
                    </li>
                    {% endif %}

                    <!-- Gestion des tâches -->
                    {% if current_user.can_manage_tasks or current_user.is_admin %}
                    <li class="nav-item">
                        <a class="nav-link {% if 'task' in request.endpoint %}active{% endif %}" href="{{ url_for('task.tasks') }}">
                            <i class="fas fa-tasks me-1"></i> Tâches
                        </a>
                    </li>
                    {% endif %}

                    <!-- Présences -->
                    <li class="nav-item">
                        <a class="nav-link {% if 'attendance' in request.endpoint %}active{% endif %}" href="{{ url_for('attendance.attendance_calendar') }}">
                            <i class="fas fa-calendar-check me-1"></i> Présences
                        </a>
                    </li>

                    <!-- Finances -->
                    {% if current_user.can_manage_finances or current_user.is_admin %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if 'finance' in request.endpoint %}active{% endif %}" href="#" id="financeDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-money-bill-wave me-1"></i> Finances
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="financeDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('finance.income') }}">
                                <i class="fas fa-arrow-up text-success me-2"></i>Encaissements
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('finance.expenses') }}">
                                <i class="fas fa-arrow-down text-danger me-2"></i>Décaissements
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('finance.reports') }}">
                                <i class="fas fa-chart-bar me-2"></i>Rapports
                            </a></li>
                        </ul>
                    </li>
                    {% endif %}

                    <!-- Gestion des utilisateurs -->
                    {% if current_user.is_admin or current_user.can_manage_users %}
                    <li class="nav-item">
                        <a class="nav-link {% if 'user' in request.endpoint %}active{% endif %}" href="{{ url_for('user.users') }}">
                            <i class="fas fa-user-cog me-1"></i> Utilisateurs
                        </a>
                    </li>
                    {% endif %}

                    <!-- Entreprise (Admin seulement) -->
                    {% if current_user.is_admin %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if 'company' in request.endpoint %}active{% endif %}" href="#" id="companyDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-building me-1"></i> Entreprise
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="companyDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('company.company_info') }}">
                                <i class="fas fa-info-circle me-2"></i>Informations
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('company.backup_management') }}">
                                <i class="fas fa-database me-2"></i>Sauvegardes
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('company.auto_backup_settings') }}">
                                <i class="fas fa-cog me-2"></i>Paramètres Auto
                            </a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle me-1"></i> {{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-user me-2"></i>Profil
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-cog me-2"></i>Paramètres
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Main Content -->
    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </div>
    
    <!-- Footer -->
    <footer class="mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2023 Gestion des Pointages. Tous droits réservés.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p>Version 1.0.0</p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
