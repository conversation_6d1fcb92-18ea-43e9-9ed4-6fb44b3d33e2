#!/usr/bin/env python3
"""
Script pour créer des données de test
"""

import sqlite3
import os
from datetime import datetime, date, timedelta

def create_test_data():
    """Créer des données de test"""
    
    # Chemin vers la base de données
    db_path = 'instance/app.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Base de données non trouvée: {db_path}")
        return False
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vérifier s'il y a déjà des tâches
        cursor.execute("SELECT COUNT(*) FROM tasks")
        task_count = cursor.fetchone()[0]
        
        if task_count > 0:
            print(f"ℹ️ Il y a déjà {task_count} tâche(s) dans la base de données")
        
        # Créer quelques tâches de test
        test_tasks = [
            {
                'title': 'Développer interface utilisateur',
                'description': 'Créer une interface moderne et responsive',
                'priority': 2,
                'status': 'in_progress',
                'due_date': (date.today() + timedelta(days=3)).isoformat(),
                'category': 'Développement',
                'tags': 'UI,Frontend,React',
                'color': '#3498db',
                'assigned_to': None
            },
            {
                'title': 'Tester les fonctionnalités',
                'description': 'Tests unitaires et d\'intégration',
                'priority': 1,
                'status': 'pending',
                'due_date': (date.today() + timedelta(days=1)).isoformat(),
                'category': 'Tests',
                'tags': 'Testing,QA',
                'color': '#e74c3c',
                'assigned_to': None
            },
            {
                'title': 'Documenter le projet',
                'description': 'Rédiger la documentation technique',
                'priority': 0,
                'status': 'pending',
                'due_date': (date.today() + timedelta(days=7)).isoformat(),
                'category': 'Documentation',
                'tags': 'Docs,Manuel',
                'color': '#2ecc71',
                'assigned_to': None
            },
            {
                'title': 'Tâche urgente',
                'description': 'Correction de bug critique',
                'priority': 3,
                'status': 'in_progress',
                'due_date': date.today().isoformat(),
                'category': 'Bug Fix',
                'tags': 'Urgent,Bug',
                'color': '#f39c12',
                'assigned_to': None
            },
            {
                'title': 'Tâche en retard',
                'description': 'Optimisation des performances',
                'priority': 2,
                'status': 'pending',
                'due_date': (date.today() - timedelta(days=2)).isoformat(),
                'category': 'Performance',
                'tags': 'Optimisation',
                'color': '#9b59b6',
                'assigned_to': None
            }
        ]
        
        # Insérer les tâches de test
        for task in test_tasks:
            cursor.execute("""
                INSERT INTO tasks (title, description, priority, status, due_date, category, tags, color, assigned_to, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task['title'],
                task['description'],
                task['priority'],
                task['status'],
                task['due_date'],
                task['category'],
                task['tags'],
                task['color'],
                task['assigned_to'],
                datetime.now().isoformat(),
                datetime.now().isoformat()
            ))
        
        conn.commit()
        
        # Vérifier le nombre de tâches après insertion
        cursor.execute("SELECT COUNT(*) FROM tasks")
        new_task_count = cursor.fetchone()[0]
        
        print(f"✅ {len(test_tasks)} tâches de test ajoutées")
        print(f"✅ Total des tâches: {new_task_count}")
        
        # Afficher les tâches créées
        cursor.execute("SELECT id, title, status, due_date FROM tasks ORDER BY id DESC LIMIT 5")
        recent_tasks = cursor.fetchall()
        
        print("\n📋 Dernières tâches créées:")
        for task in recent_tasks:
            print(f"  - ID: {task[0]}, Titre: {task[1]}, Statut: {task[2]}, Échéance: {task[3]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des données de test: {e}")
        if 'conn' in locals():
            conn.close()
        return False

if __name__ == "__main__":
    print("🔧 Création de données de test...")
    success = create_test_data()
    
    if success:
        print("🎉 Données de test créées avec succès!")
        print("🌐 Vous pouvez maintenant aller sur http://localhost:5000/tasks")
    else:
        print("💥 Création des données de test échouée!")
