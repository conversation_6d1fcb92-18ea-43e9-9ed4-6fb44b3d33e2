{% extends "base.html" %}

{% block title %}Gestion de projet{% endblock %}

{% block styles %}
<style>
    /* Variables CSS pour le thème Monday.com */
    :root {
        --primary-blue: #0073ea;
        --success-green: #00c875;
        --warning-orange: #ff9500;
        --danger-red: #e2445c;
        --purple: #a25ddc;
        --light-blue: #579bfc;
        --gray-100: #f6f7fb;
        --gray-200: #e6e9ef;
        --gray-300: #c4c4c4;
        --gray-400: #676879;
        --gray-500: #323338;
        --white: #ffffff;
        --border-radius: 8px;
        --shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    body {
        background-color: var(--gray-100);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }

    .container-fluid {
        padding: 0;
    }

    /* Header principal */
    .project-header {
        background: var(--white);
        padding: 20px 30px;
        border-bottom: 1px solid var(--gray-200);
        margin-bottom: 0;
    }

    .project-title {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 15px;
    }

    .project-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, var(--warning-orange), #ffb84d);
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
        font-weight: bold;
    }

    .project-info h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 600;
        color: var(--gray-500);
    }

    .project-info p {
        margin: 5px 0 0 0;
        color: var(--gray-400);
        font-size: 14px;
    }

    /* Navigation tabs */
    .nav-tabs {
        border: none;
        margin-bottom: 0;
    }

    .nav-tabs .nav-link {
        border: none;
        color: var(--gray-400);
        font-weight: 500;
        padding: 12px 20px;
        border-radius: 0;
        background: transparent;
        text-decoration: none;
    }

    .nav-tabs .nav-link.active {
        color: var(--primary-blue);
        background: transparent;
        border-bottom: 3px solid var(--primary-blue);
    }

    .nav-tabs .nav-link:hover {
        color: var(--primary-blue);
        border-color: transparent;
    }

    /* Toolbar */
    .toolbar {
        background: var(--white);
        padding: 15px 30px;
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .btn-toolbar {
        background: var(--primary-blue);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: var(--border-radius);
        font-weight: 500;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
    }

    .btn-toolbar:hover {
        background: #005bb5;
        color: white;
    }

    .btn-secondary-toolbar {
        background: transparent;
        color: var(--gray-400);
        border: 1px solid var(--gray-300);
        padding: 8px 16px;
        border-radius: var(--border-radius);
        font-size: 14px;
        cursor: pointer;
    }

    .btn-secondary-toolbar:hover {
        background: var(--gray-100);
        color: var(--gray-500);
    }

    /* Layout principal */
    .main-layout {
        display: flex;
        min-height: calc(100vh - 200px);
    }

    /* Sidebar */
    .sidebar {
        background: var(--white);
        border-right: 1px solid var(--gray-200);
        padding: 20px;
        width: 250px;
        flex-shrink: 0;
    }

    .sidebar h6 {
        color: var(--gray-500);
        font-weight: 600;
        margin-bottom: 15px;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .sidebar .list-group-item {
        border: none;
        padding: 8px 0;
        background: transparent;
        color: var(--gray-400);
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
    }

    .sidebar .list-group-item:hover {
        color: var(--primary-blue);
        background: transparent;
    }

    .sidebar .list-group-item.active {
        color: var(--primary-blue);
        background: transparent;
        font-weight: 600;
    }

    /* Table principale */
    .table-container {
        flex: 1;
        background: var(--white);
        overflow-x: auto;
    }

    .task-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        min-width: 1200px;
    }

    .task-table th {
        background: var(--gray-100);
        padding: 12px 15px;
        font-weight: 600;
        font-size: 13px;
        color: var(--gray-500);
        border-bottom: 1px solid var(--gray-200);
        text-align: left;
        position: sticky;
        top: 0;
        z-index: 10;
        white-space: nowrap;
    }

    .task-table td {
        padding: 12px 15px;
        border-bottom: 1px solid var(--gray-200);
        vertical-align: middle;
    }

    /* Groupes de tâches */
    .group-header {
        background: var(--gray-100);
        font-weight: 600;
        color: var(--gray-500);
        cursor: pointer;
        position: relative;
    }

    .group-header:hover {
        background: #eef1f5;
    }

    .group-toggle {
        margin-right: 10px;
        transition: transform 0.2s;
        font-size: 12px;
    }

    .group-toggle.collapsed {
        transform: rotate(-90deg);
    }

    /* Status badges */
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-align: center;
        min-width: 80px;
        display: inline-block;
        cursor: pointer;
    }

    .status-todo { background: var(--gray-200); color: var(--gray-500); }
    .status-working { background: var(--warning-orange); color: white; }
    .status-done { background: var(--success-green); color: white; }
    .status-stuck { background: var(--danger-red); color: white; }

    /* Priority badges */
    .priority-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        text-align: center;
        min-width: 60px;
        display: inline-block;
        cursor: pointer;
    }

    .priority-low { background: #e1f5fe; color: #0277bd; }
    .priority-medium { background: #fff3e0; color: #ef6c00; }
    .priority-high { background: #fce4ec; color: #c2185b; }
    .priority-urgent { background: #ffebee; color: #d32f2f; }

    /* Colonnes spéciales */
    .task-name {
        font-weight: 500;
        color: var(--gray-500);
        cursor: pointer;
    }

    .task-name:hover {
        color: var(--primary-blue);
    }

    .person-cell {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .person-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--primary-blue);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 12px;
    }

    .date-cell {
        color: var(--gray-400);
        font-size: 13px;
    }

    .budget-cell {
        font-weight: 600;
        color: var(--gray-500);
    }

    /* Actions */
    .task-actions {
        display: flex;
        gap: 8px;
        opacity: 0;
        transition: opacity 0.2s;
    }

    .task-table tr:hover .task-actions {
        opacity: 1;
    }

    .btn-action {
        background: none;
        border: none;
        color: var(--gray-400);
        font-size: 16px;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
    }

    .btn-action:hover {
        background: var(--gray-200);
        color: var(--gray-500);
    }

    /* Animations */
    .task-row {
        transition: background-color 0.2s;
    }

    .task-row:hover {
        background-color: #f8f9ff;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .project-header {
            padding: 15px 20px;
        }
        
        .toolbar {
            padding: 10px 20px;
            flex-direction: column;
            align-items: stretch;
        }
        
        .main-layout {
            flex-direction: column;
        }
        
        .sidebar {
            width: 100%;
            border-right: none;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .task-table {
            font-size: 14px;
        }
        
        .task-table th,
        .task-table td {
            padding: 8px 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header du projet -->
    <div class="project-header">
        <div class="project-title">
            <div class="project-icon">G</div>
            <div class="project-info">
                <h1>Gestion de projet</h1>
                <p>Ajouter une description de l'espace de travail</p>
            </div>
            <div class="ms-auto">
                <button class="btn btn-outline-secondary btn-sm me-2">👥 Utilisateurs</button>
                <button class="btn btn-outline-secondary btn-sm">⋯</button>
            </div>
        </div>
        
        <!-- Navigation tabs -->
        <ul class="nav nav-tabs">
            <li class="nav-item">
                <a class="nav-link active" href="#" data-view="tableau">📊 Tableau principal</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" data-view="calendar">📅 Calendrier</a>
            </li>
        </ul>
    </div>

    <!-- Toolbar -->
    <div class="toolbar">
        <button class="btn-toolbar" onclick="addNewTask()">
            ➕ Ajouter tâche
        </button>
        <button class="btn-secondary-toolbar">
            🔍 Rechercher...
        </button>
        <button class="btn-secondary-toolbar">
            👤 Personne
        </button>
        <button class="btn-secondary-toolbar">
            🔽 Filtrer
        </button>
        <button class="btn-secondary-toolbar">
            ↕️ Trier
        </button>
        <button class="btn-secondary-toolbar">
            👁️ Masquer
        </button>
        <button class="btn-secondary-toolbar">
            📊 Grouper par
        </button>
        <div class="ms-auto">
            <button class="btn-secondary-toolbar">⋯</button>
        </div>
    </div>

    <!-- Layout principal -->
    <div class="main-layout">
        <!-- Sidebar -->
        <div class="sidebar">
            <h6>🎯 Mon travail</h6>
            <div class="list-group list-group-flush">
                <a href="#" class="list-group-item active">📋 Outils</a>
                <a href="#" class="list-group-item">⭐ Favoris</a>
                <a href="#" class="list-group-item">📊 Espaces de travail</a>
            </div>

            <h6 class="mt-4">📁 Gestion de projet</h6>
            <div class="list-group list-group-flush">
                <a href="#" class="list-group-item">📋 aa</a>
                <a href="#" class="list-group-item">📋 Tableaux de bord et reporting</a>
            </div>
        </div>

        <!-- Table principale -->
        <div class="table-container">
            <table class="task-table">
                <thead>
                    <tr>
                        <th style="width: 40px;"></th>
                        <th style="width: 250px;">Tâche</th>
                        <th style="width: 120px;">Admin</th>
                        <th style="width: 100px;">Statut</th>
                        <th style="width: 120px;">Échéance</th>
                        <th style="width: 100px;">Priorité</th>
                        <th style="width: 150px;">Remarques</th>
                        <th style="width: 100px;">Budget</th>
                        <th style="width: 100px;">Fichiers</th>
                        <th style="width: 120px;">Échéancier</th>
                        <th style="width: 150px;">Dernière mise à jour</th>
                        <th style="width: 60px;"></th>
                    </tr>
                </thead>
                <tbody id="task-table-body">
                    <!-- Groupe: Dates passées -->
                    <tr class="group-header" onclick="toggleGroup('past-dates')">
                        <td colspan="12">
                            <span class="group-toggle" id="toggle-past-dates">▼</span>
                            📅 Dates passées <span class="text-muted">1 élément</span>
                        </td>
                    </tr>
                    <tr class="task-row" data-group="past-dates">
                        <td>📋</td>
                        <td class="task-name">aa</td>
                        <td class="person-cell">
                            <div class="person-avatar">A</div>
                        </td>
                        <td><span class="status-badge status-todo">To-do</span></td>
                        <td class="date-cell">aa</td>
                        <td><span class="priority-badge priority-low">Faible</span></td>
                        <td>Notes de réunion</td>
                        <td class="budget-cell">$100</td>
                        <td>📎</td>
                        <td class="date-cell">Jul 3-6</td>
                        <td class="date-cell">il y a 5 min</td>
                        <td class="task-actions">
                            <button class="btn-action" title="Modifier">✏️</button>
                            <button class="btn-action" title="Supprimer">🗑️</button>
                            <button class="btn-action" title="Imprimer">🖨️</button>
                        </td>
                    </tr>

                    <!-- Groupe: Aujourd'hui -->
                    <tr class="group-header" onclick="toggleGroup('today')">
                        <td colspan="12">
                            <span class="group-toggle" id="toggle-today">▼</span>
                            📅 Aujourd'hui <span class="text-muted">0 éléments</span>
                        </td>
                    </tr>

                    <!-- Groupe: Cette semaine -->
                    <tr class="group-header" onclick="toggleGroup('this-week')">
                        <td colspan="12">
                            <span class="group-toggle" id="toggle-this-week">▼</span>
                            📅 Cette semaine <span class="text-muted">0 éléments</span>
                        </td>
                    </tr>

                    <!-- Groupe: Semaine suivante -->
                    <tr class="group-header" onclick="toggleGroup('next-week')">
                        <td colspan="12">
                            <span class="group-toggle" id="toggle-next-week">▼</span>
                            📅 Semaine suivante <span class="text-muted">0 éléments</span>
                        </td>
                    </tr>

                    <!-- Groupe: Plus tard -->
                    <tr class="group-header" onclick="toggleGroup('later')">
                        <td colspan="12">
                            <span class="group-toggle" id="toggle-later">▼</span>
                            📅 Plus tard <span class="text-muted">0 éléments</span>
                        </td>
                    </tr>

                    <!-- Groupe: Sans date -->
                    <tr class="group-header" onclick="toggleGroup('no-date')">
                        <td colspan="12">
                            <span class="group-toggle" id="toggle-no-date">▼</span>
                            📅 Sans date <span class="text-muted">0 éléments</span>
                        </td>
                    </tr>

                    <!-- Ligne pour ajouter une nouvelle tâche -->
                    <tr class="task-row">
                        <td>➕</td>
                        <td class="task-name" style="color: var(--gray-400); cursor: pointer;" onclick="addNewTask()">+ Ajouter tâche</td>
                        <td colspan="10"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Fonction pour basculer l'affichage des groupes
function toggleGroup(groupId) {
    const toggle = document.getElementById('toggle-' + groupId);
    const rows = document.querySelectorAll(`[data-group="${groupId}"]`);

    if (toggle.classList.contains('collapsed')) {
        toggle.classList.remove('collapsed');
        toggle.textContent = '▼';
        rows.forEach(row => row.style.display = 'table-row');
    } else {
        toggle.classList.add('collapsed');
        toggle.textContent = '▶';
        rows.forEach(row => row.style.display = 'none');
    }
}

// Fonction pour ajouter une nouvelle tâche
function addNewTask() {
    alert('Fonctionnalité d\'ajout de tâche à implémenter');
}

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    console.log('Interface Monday.com chargée');
});
</script>
{% endblock %}
