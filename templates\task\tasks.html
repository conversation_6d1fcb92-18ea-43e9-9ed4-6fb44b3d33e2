{% extends "base.html" %}

{% block title %}Gestion des tâches - Gestion des Pointages{% endblock %}

{% block content %}
<!-- En-tête avec contrôles -->
<div class="row mb-4">
    <div class="col-md-6">
        <h1 class="mb-3"><i class="fas fa-tasks me-2"></i>Tableau des tâches</h1>
        <p class="text-muted">Gérez vos tâches avec une vue Kanban interactive</p>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group me-2" role="group">
            <button type="button" class="btn btn-outline-primary active" id="kanban-view-btn">
                <i class="fas fa-columns me-1"></i> Vue Kanban
            </button>
            <button type="button" class="btn btn-outline-primary" id="table-view-btn">
                <i class="fas fa-table me-1"></i> Vue Tableau
            </button>
        </div>
        <div class="btn-group" role="group">
            <a href="{{ url_for('task.add_task') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Ajouter une tâche
            </a>
            <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-filter me-1"></i> Filtrer
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item filter-option" href="#" data-filter="all">Toutes les tâches</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><h6 class="dropdown-header">Par priorité</h6></li>
                <li><a class="dropdown-item filter-option" href="#" data-filter="priority-3">Urgente</a></li>
                <li><a class="dropdown-item filter-option" href="#" data-filter="priority-2">Haute</a></li>
                <li><a class="dropdown-item filter-option" href="#" data-filter="priority-1">Normale</a></li>
                <li><a class="dropdown-item filter-option" href="#" data-filter="priority-0">Basse</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Barre de recherche et filtres -->
<div class="row mb-3">
    <div class="col-md-8">
        <input type="text" id="search-tasks" class="form-control" placeholder="🔍 Rechercher une tâche...">
    </div>
    <div class="col-md-4">
        <select id="status-filter" class="form-select">
            <option value="">Tous les statuts</option>
            <option value="pending">En attente</option>
            <option value="in_progress">En cours</option>
            <option value="completed">Terminée</option>
            <option value="cancelled">Annulée</option>
            <option value="delayed">Retardée</option>
        </select>
    </div>
</div>

<!-- Vue Kanban -->
<div id="kanban-view" class="kanban-board">
    <div class="row">
        <!-- Colonne À faire -->
        <div class="col-md-3">
            <div class="kanban-column" data-status="pending">
                <div class="kanban-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-clock me-2"></i>À faire</h5>
                    <span class="badge bg-light text-dark task-count">{{ tasks|selectattr('status', 'equalto', 'pending')|list|length }}</span>
                </div>
                <div class="kanban-body" id="pending-tasks">
                    {% for task in tasks %}
                    {% if task.status == 'pending' %}
                    <div class="task-card mb-3" data-task-id="{{ task.id }}" data-priority="{{ task.priority }}">
                        <div class="card border-start border-4" style="border-left-color: {{ task.color }} !important;">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">{{ task.title }}</h6>
                                    <span class="badge {% if task.priority == 0 %}bg-secondary{% elif task.priority == 1 %}bg-info{% elif task.priority == 2 %}bg-warning{% elif task.priority == 3 %}bg-danger{% endif %} priority-badge">
                                        {{ task.priority_display }}
                                    </span>
                                </div>
                                {% if task.description %}
                                <p class="card-text small text-muted">{{ task.description|truncate(60) }}</p>
                                {% endif %}
                                {% if task.due_date %}
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>{{ task.due_date.strftime('%d/%m/%Y') }}
                                    </small>
                                </div>
                                {% endif %}
                                {% if task.tag_list %}
                                <div class="mb-2">
                                    {% for tag in task.tag_list %}
                                    <span class="badge bg-light text-dark me-1">{{ tag }}</span>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">{{ task.created_at.strftime('%d/%m') }}</small>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('task.edit_task', id=task.id) }}" class="btn btn-outline-primary btn-sm" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info btn-sm" title="Imprimer" onclick="printTaskInfo({{ task.id }}, '{{ task.title }}', '{{ task.description|replace('\n', ' ')|replace('\r', ' ') }}', '{{ task.created_at.strftime('%d/%m/%Y') }}')">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <form action="{{ url_for('task.delete_task', id=task.id) }}" method="POST" class="d-inline">
                                            <button type="submit" class="btn btn-outline-danger btn-sm delete-confirm" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Colonne En cours -->
        <div class="col-md-3">
            <div class="kanban-column" data-status="in_progress">
                <div class="kanban-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-play me-2"></i>En cours</h5>
                    <span class="badge bg-light text-dark task-count">{{ tasks|selectattr('status', 'equalto', 'in_progress')|list|length }}</span>
                </div>
                <div class="kanban-body" id="in_progress-tasks">
                    {% for task in tasks %}
                    {% if task.status == 'in_progress' %}
                    <div class="task-card mb-3" data-task-id="{{ task.id }}" data-priority="{{ task.priority }}">
                        <div class="card border-start border-4" style="border-left-color: {{ task.color }} !important;">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">{{ task.title }}</h6>
                                    <span class="badge {% if task.priority == 0 %}bg-secondary{% elif task.priority == 1 %}bg-info{% elif task.priority == 2 %}bg-warning{% elif task.priority == 3 %}bg-danger{% endif %} priority-badge">
                                        {{ task.priority_display }}
                                    </span>
                                </div>
                                {% if task.description %}
                                <p class="card-text small text-muted">{{ task.description|truncate(60) }}</p>
                                {% endif %}
                                {% if task.due_date %}
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>{{ task.due_date.strftime('%d/%m/%Y') }}
                                    </small>
                                </div>
                                {% endif %}
                                {% if task.tag_list %}
                                <div class="mb-2">
                                    {% for tag in task.tag_list %}
                                    <span class="badge bg-light text-dark me-1">{{ tag }}</span>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">{{ task.created_at.strftime('%d/%m') }}</small>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('task.edit_task', id=task.id) }}" class="btn btn-outline-primary btn-sm" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info btn-sm" title="Imprimer" onclick="printTaskInfo({{ task.id }}, '{{ task.title }}', '{{ task.description|replace('\n', ' ')|replace('\r', ' ') }}', '{{ task.created_at.strftime('%d/%m/%Y') }}')">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <form action="{{ url_for('task.delete_task', id=task.id) }}" method="POST" class="d-inline">
                                            <button type="submit" class="btn btn-outline-danger btn-sm delete-confirm" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Colonne Terminée -->
        <div class="col-md-3">
            <div class="kanban-column" data-status="completed">
                <div class="kanban-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-check me-2"></i>Terminée</h5>
                    <span class="badge bg-light text-dark task-count">{{ tasks|selectattr('status', 'equalto', 'completed')|list|length }}</span>
                </div>
                <div class="kanban-body" id="completed-tasks">
                    {% for task in tasks %}
                    {% if task.status == 'completed' %}
                    <div class="task-card mb-3" data-task-id="{{ task.id }}" data-priority="{{ task.priority }}">
                        <div class="card border-start border-4" style="border-left-color: {{ task.color }} !important;">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0 text-decoration-line-through">{{ task.title }}</h6>
                                    <span class="badge bg-success priority-badge">
                                        <i class="fas fa-check"></i>
                                    </span>
                                </div>
                                {% if task.description %}
                                <p class="card-text small text-muted">{{ task.description|truncate(60) }}</p>
                                {% endif %}
                                {% if task.due_date %}
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>{{ task.due_date.strftime('%d/%m/%Y') }}
                                    </small>
                                </div>
                                {% endif %}
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">{{ task.created_at.strftime('%d/%m') }}</small>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('task.edit_task', id=task.id) }}" class="btn btn-outline-primary btn-sm" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info btn-sm" title="Imprimer" onclick="printTaskInfo({{ task.id }}, '{{ task.title }}', '{{ task.description|replace('\n', ' ')|replace('\r', ' ') }}', '{{ task.created_at.strftime('%d/%m/%Y') }}')">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <form action="{{ url_for('task.delete_task', id=task.id) }}" method="POST" class="d-inline">
                                            <button type="submit" class="btn btn-outline-danger btn-sm delete-confirm" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Colonne Retardée/Annulée -->
        <div class="col-md-3">
            <div class="kanban-column" data-status="other">
                <div class="kanban-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Autres</h5>
                    <span class="badge bg-dark text-light task-count">{{ tasks|selectattr('status', 'in', ['delayed', 'cancelled'])|list|length }}</span>
                </div>
                <div class="kanban-body" id="other-tasks">
                    {% for task in tasks %}
                    {% if task.status in ['delayed', 'cancelled'] %}
                    <div class="task-card mb-3" data-task-id="{{ task.id }}" data-priority="{{ task.priority }}">
                        <div class="card border-start border-4" style="border-left-color: {{ task.color }} !important;">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">{{ task.title }}</h6>
                                    <span class="badge {% if task.status == 'delayed' %}bg-warning text-dark{% else %}bg-secondary{% endif %} priority-badge">
                                        {{ task.status_display }}
                                    </span>
                                </div>
                                {% if task.description %}
                                <p class="card-text small text-muted">{{ task.description|truncate(60) }}</p>
                                {% endif %}
                                {% if task.due_date %}
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>{{ task.due_date.strftime('%d/%m/%Y') }}
                                    </small>
                                </div>
                                {% endif %}
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">{{ task.created_at.strftime('%d/%m') }}</small>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('task.edit_task', id=task.id) }}" class="btn btn-outline-primary btn-sm" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info btn-sm" title="Imprimer" onclick="printTaskInfo({{ task.id }}, '{{ task.title }}', '{{ task.description|replace('\n', ' ')|replace('\r', ' ') }}', '{{ task.created_at.strftime('%d/%m/%Y') }}')">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <form action="{{ url_for('task.delete_task', id=task.id) }}" method="POST" class="d-inline">
                                            <button type="submit" class="btn btn-outline-danger btn-sm delete-confirm" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Vue Tableau (cachée par défaut) -->
<div id="table-view" class="d-none">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-table me-2"></i>Vue Tableau</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Titre</th>
                            <th>Statut</th>
                            <th>Priorité</th>
                            <th>Échéance</th>
                            <th>Catégorie</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for task in tasks %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-2" style="width: 4px; height: 30px; background-color: {{ task.color }};"></div>
                                    <div>
                                        <strong>{{ task.title }}</strong>
                                        {% if task.description %}
                                        <br><small class="text-muted">{{ task.description|truncate(50) }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge {% if task.status == 'pending' %}bg-secondary{% elif task.status == 'in_progress' %}bg-primary{% elif task.status == 'completed' %}bg-success{% elif task.status == 'delayed' %}bg-warning text-dark{% else %}bg-danger{% endif %}">
                                    {{ task.status_display }}
                                </span>
                            </td>
                            <td>
                                <span class="badge {% if task.priority == 0 %}bg-secondary{% elif task.priority == 1 %}bg-info{% elif task.priority == 2 %}bg-warning{% elif task.priority == 3 %}bg-danger{% endif %}">
                                    {{ task.priority_display }}
                                </span>
                            </td>
                            <td>
                                {% if task.due_date %}
                                {{ task.due_date.strftime('%d/%m/%Y') }}
                                {% else %}
                                <span class="text-muted">Non définie</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if task.category %}
                                <span class="badge bg-light text-dark">{{ task.category }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ url_for('task.edit_task', id=task.id) }}" class="btn btn-outline-primary" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-info" title="Imprimer" onclick="printTaskInfo({{ task.id }}, '{{ task.title }}', '{{ task.description|replace('\n', ' ')|replace('\r', ' ') }}', '{{ task.created_at.strftime('%d/%m/%Y') }}')">
                                        <i class="fas fa-print"></i>
                                    </button>
                                    <form action="{{ url_for('task.delete_task', id=task.id) }}" method="POST" class="d-inline">
                                        <button type="submit" class="btn btn-outline-danger delete-confirm" title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="6" class="text-center">Aucune tâche trouvée</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Liens rapides -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-link me-2"></i>Liens rapides</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <a href="{{ url_for('task.daily_tasks') }}" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-calendar-day me-2"></i>Tâches quotidiennes
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('task.assign_task') }}" class="btn btn-outline-success w-100 mb-2">
                            <i class="fas fa-user-check me-2"></i>Assigner une tâche
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="{{ url_for('task.task_checkboxes') }}" class="btn btn-outline-info w-100 mb-2">
                            <i class="fas fa-tasks me-2"></i>Liste des 50 tâches
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    .kanban-board {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .kanban-column {
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .kanban-header {
        padding: 15px;
        border-radius: 8px 8px 0 0;
        display: flex;
        justify-content: between;
        align-items: center;
    }

    .kanban-header h5 {
        flex-grow: 1;
    }

    .kanban-body {
        padding: 15px;
        min-height: 400px;
        max-height: 600px;
        overflow-y: auto;
    }

    .task-card {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .task-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .task-card .card {
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .task-card:hover .card {
        border-color: #007bff;
    }

    .priority-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }

    .task-count {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
    }

    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    /* Animations pour le drag & drop */
    .task-card.dragging {
        opacity: 0.5;
        transform: rotate(5deg);
    }

    .kanban-body.drag-over {
        background-color: #e3f2fd;
        border: 2px dashed #2196f3;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .kanban-board .row {
            flex-direction: column;
        }

        .kanban-column {
            margin-bottom: 15px;
        }

        .kanban-body {
            min-height: 200px;
            max-height: 300px;
        }
    }

    /* Filtres actifs */
    .filter-active {
        background-color: #007bff !important;
        color: white !important;
    }

    /* Vue tableau */
    .table th {
        background-color: #f8f9fa;
        border-top: none;
        font-weight: 600;
    }

    .table td {
        vertical-align: middle;
    }

    /* Badges personnalisés */
    .badge {
        font-size: 0.75rem;
    }

    /* Boutons de vue */
    .btn-group .btn.active {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    // Variables globales
    let currentView = 'kanban';
    let currentFilter = 'all';

    // Initialisation
    document.addEventListener('DOMContentLoaded', function() {
        initializeViewToggle();
        initializeSearch();
        initializeFilters();
        initializeDragAndDrop();
        updateTaskCounts();
    });

    // Basculer entre les vues
    function initializeViewToggle() {
        const kanbanBtn = document.getElementById('kanban-view-btn');
        const tableBtn = document.getElementById('table-view-btn');
        const kanbanView = document.getElementById('kanban-view');
        const tableView = document.getElementById('table-view');

        kanbanBtn.addEventListener('click', function() {
            currentView = 'kanban';
            kanbanBtn.classList.add('active');
            tableBtn.classList.remove('active');
            kanbanView.classList.remove('d-none');
            tableView.classList.add('d-none');
        });

        tableBtn.addEventListener('click', function() {
            currentView = 'table';
            tableBtn.classList.add('active');
            kanbanBtn.classList.remove('active');
            tableView.classList.remove('d-none');
            kanbanView.classList.add('d-none');
        });
    }

    // Recherche en temps réel
    function initializeSearch() {
        const searchInput = document.getElementById('search-tasks');
        const statusFilter = document.getElementById('status-filter');

        searchInput.addEventListener('input', function() {
            filterTasks();
        });

        statusFilter.addEventListener('change', function() {
            filterTasks();
        });
    }

    // Filtrer les tâches
    function filterTasks() {
        const searchTerm = document.getElementById('search-tasks').value.toLowerCase();
        const statusFilter = document.getElementById('status-filter').value;
        const taskCards = document.querySelectorAll('.task-card');
        const tableRows = document.querySelectorAll('#table-view tbody tr');

        // Filtrer les cartes Kanban
        taskCards.forEach(card => {
            const title = card.querySelector('.card-title').textContent.toLowerCase();
            const description = card.querySelector('.card-text') ?
                card.querySelector('.card-text').textContent.toLowerCase() : '';
            const taskStatus = card.closest('.kanban-column').dataset.status;

            const matchesSearch = title.includes(searchTerm) || description.includes(searchTerm);
            const matchesStatus = !statusFilter || taskStatus === statusFilter ||
                (statusFilter === 'other' && ['delayed', 'cancelled'].includes(taskStatus));

            if (matchesSearch && matchesStatus) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });

        // Filtrer les lignes du tableau
        tableRows.forEach(row => {
            if (row.cells.length === 1) return; // Skip "no tasks" row

            const title = row.cells[0].textContent.toLowerCase();
            const status = row.cells[1].textContent.toLowerCase();

            const matchesSearch = title.includes(searchTerm);
            const matchesStatus = !statusFilter || status.includes(statusFilter);

            if (matchesSearch && matchesStatus) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });

        updateTaskCounts();
    }

    // Initialiser les filtres
    function initializeFilters() {
        const filterOptions = document.querySelectorAll('.filter-option');

        filterOptions.forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const filter = this.dataset.filter;
                applyPriorityFilter(filter);
            });
        });
    }

    // Appliquer le filtre de priorité
    function applyPriorityFilter(filter) {
        const taskCards = document.querySelectorAll('.task-card');

        taskCards.forEach(card => {
            if (filter === 'all') {
                card.style.display = 'block';
            } else if (filter.startsWith('priority-')) {
                const priority = filter.split('-')[1];
                const cardPriority = card.dataset.priority;

                if (cardPriority === priority) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            }
        });

        updateTaskCounts();
    }

    // Mettre à jour les compteurs
    function updateTaskCounts() {
        const columns = document.querySelectorAll('.kanban-column');

        columns.forEach(column => {
            const visibleCards = column.querySelectorAll('.task-card:not([style*="display: none"])');
            const countBadge = column.querySelector('.task-count');
            if (countBadge) {
                countBadge.textContent = visibleCards.length;
            }
        });
    }

    // Initialiser le drag & drop (fonctionnalité future)
    function initializeDragAndDrop() {
        // Cette fonctionnalité peut être ajoutée plus tard
        // pour permettre de déplacer les tâches entre les colonnes
    }

    // Fonction d'impression améliorée
    function printTaskInfo(id, title, description, createdAt) {
        let printWindow = window.open('', '_blank');

        let content = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Fiche Tâche - ${title}</title>
                <meta charset="UTF-8">
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 20px;
                        color: #333;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 2px solid #007bff;
                        padding-bottom: 20px;
                    }
                    .header h1 {
                        color: #007bff;
                        margin-bottom: 10px;
                    }
                    .info-table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 30px;
                    }
                    .info-table th, .info-table td {
                        border: 1px solid #ddd;
                        padding: 12px;
                        text-align: left;
                    }
                    .info-table th {
                        background-color: #f8f9fa;
                        width: 30%;
                        font-weight: 600;
                        color: #495057;
                    }
                    .footer {
                        margin-top: 40px;
                        text-align: center;
                        font-size: 12px;
                        color: #6c757d;
                        border-top: 1px solid #dee2e6;
                        padding-top: 20px;
                    }
                    @media print {
                        body { margin: 0; }
                        .header { page-break-inside: avoid; }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>📋 Fiche Tâche</h1>
                    <p>Date d'impression: ${new Date().toLocaleDateString('fr-FR')}</p>
                </div>

                <table class="info-table">
                    <tr>
                        <th>🆔 ID</th>
                        <td>${id}</td>
                    </tr>
                    <tr>
                        <th>📝 Titre</th>
                        <td><strong>${title}</strong></td>
                    </tr>
                    <tr>
                        <th>📄 Description</th>
                        <td>${description || 'Aucune description'}</td>
                    </tr>
                    <tr>
                        <th>📅 Date de création</th>
                        <td>${createdAt}</td>
                    </tr>
                </table>

                <div class="footer">
                    <p><strong>Gestion des Pointages</strong> - Document généré automatiquement</p>
                    <p>© ${new Date().getFullYear()} - Tous droits réservés</p>
                </div>
            </body>
            </html>
        `;

        printWindow.document.open();
        printWindow.document.write(content);
        printWindow.document.close();

        printWindow.onload = function() {
            printWindow.print();
        };
    }

    // Confirmation de suppression
    document.addEventListener('click', function(e) {
        if (e.target.closest('.delete-confirm')) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')) {
                e.preventDefault();
            }
        }
    });
</script>
{% endblock %}