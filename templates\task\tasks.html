{% extends "base.html" %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <!-- En-tête avec boutons d'action -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">
            <i class="fas fa-tasks text-primary me-2"></i>
            Gestion des Tâches
        </h2>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-filter me-1"></i> Filtrer
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-sort me-1"></i> Trier
            </button>
            <a href="{{ url_for('task.add_task') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus me-1"></i> Ajouter élément
            </a>
        </div>
    </div>

    <!-- Groupes de tâches -->
    {% set task_groups = {
        'nouveau': {'title': 'Nouvelles Tâches', 'color': 'primary', 'tasks': []},
        'en_cours': {'title': 'Tâches en Cours', 'color': 'warning', 'tasks': []},
        'termine': {'title': 'Tâches Terminées', 'color': 'success', 'tasks': []},
        'annule': {'title': 'Tâches Annulées', 'color': 'danger', 'tasks': []},
        'autres': {'title': 'Autres Tâches', 'color': 'secondary', 'tasks': []}
    } %}

    {% for task in tasks %}
        {% if task.status in task_groups %}
            {% set _ = task_groups[task.status]['tasks'].append(task) %}
        {% else %}
            {% set _ = task_groups['autres']['tasks'].append(task) %}
        {% endif %}
    {% endfor %}

    {% for group_key, group in task_groups.items() %}
    {% if group.tasks %}
    <div class="task-group mb-4">
        <div class="group-header" data-bs-toggle="collapse" data-bs-target="#group-{{ group_key }}" aria-expanded="true">
            <div class="d-flex align-items-center">
                <div class="group-indicator bg-{{ group.color }}"></div>
                <i class="fas fa-chevron-down collapse-icon me-2"></i>
                <h5 class="mb-0 me-3">{{ group.title }}</h5>
                <span class="badge bg-{{ group.color }} rounded-pill">{{ group.tasks|length }}</span>
            </div>
        </div>
        
        <div class="collapse show" id="group-{{ group_key }}">
            <div class="group-content">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="5%"></th>
                                <th width="25%">Élément</th>
                                <th width="15%">Personne</th>
                                <th width="15%">Statut</th>
                                <th width="15%">Période</th>
                                <th width="15%">Échéances</th>
                                <th width="10%">Menu déroulant</th>
                                <th width="5%">
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in group.tasks %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-grip-vertical text-muted me-2"></i>
                                        <div class="flex-grow-1">
                                            <div class="fw-medium editable-field"
                                                 data-field="title"
                                                 data-task-id="{{ task.id }}"
                                                 onclick="makeEditable(this)">
                                                {{ task.title }}
                                            </div>
                                            {% if task.description %}
                                            <small class="text-muted editable-field"
                                                   data-field="description"
                                                   data-task-id="{{ task.id }}"
                                                   onclick="makeEditable(this)">
                                                {{ task.description[:50] }}{% if task.description|length > 50 %}...{% endif %}
                                            </small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-secondary text-white me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <span class="text-muted">Non assigné</span>
                                    </div>
                                </td>
                                <td>
                                    <select class="form-select form-select-sm status-select"
                                            data-task-id="{{ task.id }}"
                                            onchange="updateTaskStatus(this)">
                                        <option value="nouveau" {% if task.status == 'nouveau' %}selected{% endif %}>Nouveau</option>
                                        <option value="en_cours" {% if task.status == 'en_cours' %}selected{% endif %}>En cours</option>
                                        <option value="termine" {% if task.status == 'termine' %}selected{% endif %}>Terminé</option>
                                        <option value="annule" {% if task.status == 'annule' %}selected{% endif %}>Bloqué</option>
                                    </select>
                                </td>
                                <td>
                                    <input type="date"
                                           class="form-control form-control-sm date-input"
                                           data-task-id="{{ task.id }}"
                                           value="{{ task.due_date.strftime('%Y-%m-%d') if task.due_date else '' }}"
                                           onchange="updateTaskDate(this)">
                                </td>
                                <td>
                                    {% if task.due_date %}
                                    <div class="deadline-indicator">
                                        <div class="deadline-bar"></div>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-success" onclick="sendTaskWhatsApp({{ task.id }}, '{{ task.title }}')" title="WhatsApp">
                                            <i class="fab fa-whatsapp"></i>
                                        </button>
                                        <button class="btn btn-outline-primary" onclick="sendTaskEmail({{ task.id }}, '{{ task.title }}')" title="Email">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ url_for('task.view_task', id=task.id) }}">
                                                    <i class="fas fa-eye me-2"></i>Voir
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('task.edit_task', id=task.id) }}">
                                                    <i class="fas fa-edit me-2"></i>Modifier
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('task.print_task', id=task.id) }}" target="_blank">
                                                    <i class="fas fa-print me-2"></i>Imprimer
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="confirmDelete({{ task.id }}, '{{ task.title }}')">
                                                    <i class="fas fa-trash me-2"></i>Supprimer
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                                <td></td>
                            </tr>
                            {% endfor %}
                            <tr class="add-row">
                                <td colspan="8">
                                    <a href="{{ url_for('task.add_task') }}" class="btn btn-link text-decoration-none p-2">
                                        <i class="fas fa-plus me-2"></i>Ajouter élément
                                    </a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    {% endfor %}

    {% if not tasks %}
    <div class="text-center py-5">
        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">Aucune tâche trouvée</h4>
        <p class="text-muted">Commencez par créer votre première tâche.</p>
        <a href="{{ url_for('task.add_task') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Créer une tâche
        </a>
    </div>
    {% endif %}
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la tâche <strong id="task-title"></strong> ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <a href="#" id="delete-link" class="btn btn-danger">Supprimer</a>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block styles %}
<style>
.task-group {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.group-header {
    background: #f8f9fa;
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s;
}

.group-header:hover {
    background: #e9ecef;
}

.group-indicator {
    width: 4px;
    height: 20px;
    border-radius: 2px;
    margin-right: 12px;
}

.collapse-icon {
    transition: transform 0.2s;
    color: #6c757d;
}

.group-header[aria-expanded="false"] .collapse-icon {
    transform: rotate(-90deg);
}

.group-content {
    background: white;
}

.avatar-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-nouveau {
    background: #e3f2fd;
    color: #1976d2;
}

.status-en_cours {
    background: #fff3e0;
    color: #f57c00;
}

.status-termine {
    background: #e8f5e8;
    color: #388e3c;
}

.status-annule {
    background: #ffebee;
    color: #d32f2f;
}

.period-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.deadline-indicator {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.deadline-bar {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #ff9800, #f44336);
    width: 60%;
    border-radius: 4px;
}

.add-row {
    background: #f8f9fa;
}

.add-row:hover {
    background: #e9ecef;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 13px;
    padding: 12px 8px;
}

.table td {
    padding: 12px 8px;
    vertical-align: middle;
    border-top: 1px solid #f1f3f4;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

.btn-group .btn {
    border-radius: 6px;
    margin-left: 4px;
}

.btn-group .btn:first-child {
    margin-left: 0;
}
</style>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(taskId, taskTitle) {
    document.getElementById('task-title').textContent = taskTitle;
    document.getElementById('delete-link').href = '/task/delete/' + taskId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Animation pour les groupes qui se plient/déplient
document.addEventListener('DOMContentLoaded', function() {
    const collapseElements = document.querySelectorAll('.collapse');
    collapseElements.forEach(function(element) {
        element.addEventListener('show.bs.collapse', function() {
            const header = document.querySelector('[data-bs-target="#' + element.id + '"]');
            header.setAttribute('aria-expanded', 'true');
        });

        element.addEventListener('hide.bs.collapse', function() {
            const header = document.querySelector('[data-bs-target="#' + element.id + '"]');
            header.setAttribute('aria-expanded', 'false');
        });
    });
});

// Édition en ligne
function makeEditable(element) {
    const currentText = element.textContent.trim();
    const field = element.dataset.field;
    const taskId = element.dataset.taskId;

    // Créer un input
    const input = document.createElement(field === 'description' ? 'textarea' : 'input');
    input.value = currentText;
    input.className = 'form-control form-control-sm';
    if (field === 'description') {
        input.rows = 2;
    }

    // Remplacer le texte par l'input
    element.innerHTML = '';
    element.appendChild(input);
    input.focus();
    input.select();

    // Sauvegarder en appuyant sur Entrée
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            saveField(element, input, field, taskId);
        }
    });

    // Sauvegarder en perdant le focus
    input.addEventListener('blur', function() {
        saveField(element, input, field, taskId);
    });
}

function saveField(element, input, field, taskId) {
    const newValue = input.value.trim();

    // Envoyer la mise à jour au serveur
    fetch(`/task/update_field/${taskId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        },
        body: JSON.stringify({
            field: field,
            value: newValue
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Restaurer le texte
            element.textContent = newValue || 'Cliquez pour modifier';
            showNotification('Modification sauvegardée', 'success');
        } else {
            showNotification('Erreur lors de la sauvegarde', 'error');
            element.textContent = element.dataset.originalValue || 'Erreur';
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showNotification('Erreur de connexion', 'error');
        element.textContent = element.dataset.originalValue || 'Erreur';
    });
}

// Mise à jour du statut
function updateTaskStatus(selectElement) {
    const taskId = selectElement.dataset.taskId;
    const newStatus = selectElement.value;

    fetch(`/task/update_field/${taskId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        },
        body: JSON.stringify({
            field: 'status',
            value: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Statut mis à jour', 'success');
            // Optionnel: déplacer la ligne vers le bon groupe
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification('Erreur lors de la mise à jour', 'error');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showNotification('Erreur de connexion', 'error');
    });
}

// Mise à jour de la date
function updateTaskDate(inputElement) {
    const taskId = inputElement.dataset.taskId;
    const newDate = inputElement.value;

    fetch(`/task/update_field/${taskId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        },
        body: JSON.stringify({
            field: 'due_date',
            value: newDate
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Date mise à jour', 'success');
        } else {
            showNotification('Erreur lors de la mise à jour', 'error');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showNotification('Erreur de connexion', 'error');
    });
}

// Envoi par WhatsApp
function sendTaskWhatsApp(taskId, taskTitle) {
    const message = `📋 Tâche: ${taskTitle}\n🔗 Lien: ${window.location.origin}/task/view/${taskId}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

// Envoi par Email
function sendTaskEmail(taskId, taskTitle) {
    const subject = `Tâche: ${taskTitle}`;
    const body = `Bonjour,\n\nVeuillez consulter la tâche suivante:\n\nTitre: ${taskTitle}\nLien: ${window.location.origin}/task/view/${taskId}\n\nCordialement`;

    const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoUrl;
}

// Notifications
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Supprimer automatiquement après 3 secondes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
