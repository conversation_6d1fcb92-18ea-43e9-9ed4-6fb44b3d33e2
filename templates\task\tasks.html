{% extends "base.html" %}

{% block content %}
<div class="container-fluid p-0">
    <div class="row g-0">
        <!-- Sidebar -->
        <div class="col-md-2 sidebar">
            <div class="sidebar-header">
                <h6 class="mb-0">
                    <i class="fas fa-tasks text-primary me-2"></i>
                    Gestion des Tâches
                </h6>
            </div>
            <div class="sidebar-content">
                <div class="sidebar-section">
                    <h6 class="sidebar-title">Actions</h6>
                    <a href="{{ url_for('task.add_task') }}" class="sidebar-link">
                        <i class="fas fa-plus me-2"></i> Ajouter élément
                    </a>
                    <button type="button" class="sidebar-link" onclick="toggleFilter()">
                        <i class="fas fa-filter me-2"></i> Filtrer
                    </button>
                    <button type="button" class="sidebar-link" onclick="toggleSort()">
                        <i class="fas fa-sort me-2"></i> Trier
                    </button>
                </div>

                <div class="sidebar-section">
                    <h6 class="sidebar-title">Statistiques</h6>
                    <div class="stat-item">
                        <span class="stat-label">Total:</span>
                        <span class="stat-value">{{ tasks|length }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Nouvelles:</span>
                        <span class="stat-value text-primary">{{ tasks|selectattr('status', 'equalto', 'nouveau')|list|length }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">En cours:</span>
                        <span class="stat-value text-warning">{{ tasks|selectattr('status', 'equalto', 'en_cours')|list|length }}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Terminées:</span>
                        <span class="stat-value text-success">{{ tasks|selectattr('status', 'equalto', 'termine')|list|length }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <div class="col-md-10 main-content">
            <div class="content-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Liste des Tâches</h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-secondary" onclick="toggleView('table')">
                            <i class="fas fa-table"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="toggleView('grid')">
                            <i class="fas fa-th"></i>
                        </button>
                    </div>
                </div>
            </div>

    <!-- Groupes de tâches -->
    {% set task_groups = {
        'nouveau': {'title': 'Nouvelles Tâches', 'color': 'primary', 'tasks': []},
        'en_cours': {'title': 'Tâches en Cours', 'color': 'warning', 'tasks': []},
        'termine': {'title': 'Tâches Terminées', 'color': 'success', 'tasks': []},
        'annule': {'title': 'Tâches Annulées', 'color': 'danger', 'tasks': []},
        'autres': {'title': 'Autres Tâches', 'color': 'secondary', 'tasks': []}
    } %}
    
    {% for task in tasks %}
        {% if task.status in task_groups %}
            {% set _ = task_groups[task.status]['tasks'].append(task) %}
        {% else %}
            {% set _ = task_groups['autres']['tasks'].append(task) %}
        {% endif %}
    {% endfor %}

    {% for group_key, group in task_groups.items() %}
    {% if group.tasks %}
    <div class="task-group mb-4">
        <div class="group-header" data-bs-toggle="collapse" data-bs-target="#group-{{ group_key }}" aria-expanded="true">
            <div class="d-flex align-items-center">
                <div class="group-indicator bg-{{ group.color }}"></div>
                <i class="fas fa-chevron-down collapse-icon me-2"></i>
                <h5 class="mb-0 me-3">{{ group.title }}</h5>
                <span class="badge bg-{{ group.color }} rounded-pill">{{ group.tasks|length }}</span>
            </div>
        </div>
        
        <div class="collapse show" id="group-{{ group_key }}">
            <div class="group-content">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="5%"></th>
                                <th width="25%">Élément</th>
                                <th width="15%">Personne</th>
                                <th width="15%">Statut</th>
                                <th width="15%">Période</th>
                                <th width="15%">Échéances</th>
                                <th width="10%">Menu déroulant</th>
                                <th width="5%">
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in group.tasks %}
                            <tr data-task-id="{{ task.id }}">
                                <td>
                                    <input type="checkbox" class="form-check-input">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-grip-vertical text-muted me-2"></i>
                                        <div class="flex-grow-1">
                                            <div class="fw-medium editable-title" 
                                                 data-field="title" 
                                                 data-task-id="{{ task.id }}"
                                                 onclick="makeEditable(this)">
                                                {{ task.title }}
                                            </div>
                                            {% if task.description %}
                                            <small class="text-muted editable-description" 
                                                   data-field="description" 
                                                   data-task-id="{{ task.id }}"
                                                   onclick="makeEditable(this)">
                                                {{ task.description[:50] }}{% if task.description|length > 50 %}...{% endif %}
                                            </small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle bg-secondary text-white me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <select class="form-select form-select-sm person-select"
                                                data-task-id="{{ task.id }}"
                                                onchange="updateTaskAssignee(this)">
                                            <option value="">Non assigné</option>
                                            {% if employees %}
                                                {% for employee in employees %}
                                                <option value="{{ employee.id }}" {% if task.assigned_to == employee.id %}selected{% endif %}>
                                                    {{ employee.nom }} {{ employee.prenom }}
                                                </option>
                                                {% endfor %}
                                            {% endif %}
                                        </select>
                                    </div>
                                </td>
                                <td>
                                    <select class="form-select form-select-sm status-select" 
                                            data-task-id="{{ task.id }}" 
                                            onchange="updateTaskStatus(this)">
                                        <option value="nouveau" {% if task.status == 'nouveau' %}selected{% endif %}>Nouveau</option>
                                        <option value="en_cours" {% if task.status == 'en_cours' %}selected{% endif %}>En cours</option>
                                        <option value="termine" {% if task.status == 'termine' %}selected{% endif %}>Terminé</option>
                                        <option value="annule" {% if task.status == 'annule' %}selected{% endif %}>Bloqué</option>
                                    </select>
                                </td>
                                <td>
                                    <input type="date" 
                                           class="form-control form-control-sm date-input" 
                                           data-task-id="{{ task.id }}"
                                           value="{{ task.due_date.strftime('%Y-%m-%d') if task.due_date else '' }}"
                                           onchange="updateTaskDate(this)">
                                </td>
                                <td>
                                    {% if task.due_date %}
                                    <div class="deadline-indicator">
                                        <div class="deadline-bar"></div>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-success" onclick="sendTaskWhatsApp({{ task.id }}, '{{ task.title }}')" title="WhatsApp">
                                            <i class="fab fa-whatsapp"></i>
                                        </button>
                                        <button class="btn btn-outline-primary" onclick="sendTaskEmail({{ task.id }}, '{{ task.title }}')" title="Email">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ url_for('task.edit_task', id=task.id) }}">
                                                    <i class="fas fa-edit me-2"></i>Modifier
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ url_for('task.print_task', id=task.id) }}" target="_blank">
                                                    <i class="fas fa-print me-2"></i>Imprimer
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="confirmDelete({{ task.id }}, '{{ task.title }}')">
                                                    <i class="fas fa-trash me-2"></i>Supprimer
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-success save-btn" style="display: none;" onclick="saveTaskChanges({{ task.id }})" title="Sauvegarder">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                            <tr class="add-row">
                                <td colspan="8">
                                    <a href="{{ url_for('task.add_task') }}" class="btn btn-link text-decoration-none p-2">
                                        <i class="fas fa-plus me-2"></i>Ajouter élément
                                    </a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    {% endfor %}

    {% if not tasks %}
    <div class="text-center py-5">
        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">Aucune tâche trouvée</h4>
        <p class="text-muted">Commencez par créer votre première tâche.</p>
        <a href="{{ url_for('task.add_task') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Créer une tâche
        </a>
    </div>
    {% endif %}
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la tâche <strong id="task-title"></strong> ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <a href="#" id="delete-link" class="btn btn-danger">Supprimer</a>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block styles %}
<style>
/* Sidebar Styles */
.sidebar {
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    min-height: 100vh;
    font-size: 13px;
}

.sidebar-header {
    padding: 15px;
    background: #e9ecef;
    border-bottom: 1px solid #dee2e6;
}

.sidebar-header h6 {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
}

.sidebar-content {
    padding: 15px 0;
}

.sidebar-section {
    margin-bottom: 20px;
    padding: 0 15px;
}

.sidebar-title {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    color: #6c757d;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
}

.sidebar-link {
    display: block;
    padding: 6px 0;
    color: #495057;
    text-decoration: none;
    font-size: 12px;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    transition: color 0.2s;
}

.sidebar-link:hover {
    color: #0d6efd;
    text-decoration: none;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 3px 0;
    font-size: 11px;
}

.stat-label {
    color: #6c757d;
}

.stat-value {
    font-weight: 600;
}

/* Main Content */
.main-content {
    background: white;
    min-height: 100vh;
}

.content-header {
    padding: 15px 20px;
    background: white;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 0;
}

.content-header h5 {
    font-size: 16px;
    font-weight: 600;
}

/* Task Groups */
.task-group {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    background: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin: 15px 20px;
    font-size: 12px;
}

.group-header {
    background: #f8f9fa;
    padding: 10px 12px;
    cursor: pointer;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s;
    font-size: 13px;
}

.group-header:hover {
    background: #e9ecef;
}

.group-indicator {
    width: 3px;
    height: 16px;
    border-radius: 2px;
    margin-right: 10px;
}

.collapse-icon {
    transition: transform 0.2s;
    color: #6c757d;
    font-size: 11px;
}

.group-header[aria-expanded="false"] .collapse-icon {
    transform: rotate(-90deg);
}

.group-content {
    background: white;
}

.group-header h5 {
    font-size: 13px;
    font-weight: 600;
}

.group-header .badge {
    font-size: 10px;
}

.avatar-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.deadline-indicator {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.deadline-bar {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #ff9800, #f44336);
    width: 60%;
    border-radius: 4px;
}

.add-row {
    background: #f8f9fa;
}

.add-row:hover {
    background: #e9ecef;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 11px;
    padding: 8px 6px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.table td {
    padding: 8px 6px;
    vertical-align: middle;
    border-top: 1px solid #f1f3f4;
    font-size: 11px;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

.table .fw-medium {
    font-size: 12px;
    font-weight: 500;
}

.table small {
    font-size: 10px;
}

.btn-group .btn {
    border-radius: 4px;
    margin-left: 2px;
    font-size: 10px;
    padding: 4px 6px;
}

.btn-group .btn:first-child {
    margin-left: 0;
}

.btn-group .btn i {
    font-size: 10px;
}

.editable-title, .editable-description {
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s;
    font-size: 11px;
}

.editable-title:hover, .editable-description:hover {
    background-color: #e3f2fd;
    border: 1px dashed #0d6efd;
}

.form-control-sm, .form-select-sm {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 10px;
    padding: 3px 6px;
}

.form-control-sm:focus, .form-select-sm:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.15rem rgba(13, 110, 253, 0.25);
}

.status-select {
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
    padding: 3px 6px;
    border-radius: 15px;
}

.date-input {
    font-size: 10px;
    padding: 3px 6px;
}

.person-select {
    font-size: 10px;
    padding: 3px 6px;
}

.avatar-circle {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.save-btn {
    font-size: 10px;
    padding: 3px 6px;
}

.add-row a {
    font-size: 11px;
    padding: 8px 12px;
}
</style>
{% endblock %}

{% block scripts %}
<script>
// Variables globales pour le suivi des modifications
let editingElements = new Set();

function confirmDelete(taskId, taskTitle) {
    document.getElementById('task-title').textContent = taskTitle;
    document.getElementById('delete-link').href = '/task/delete/' + taskId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Animation pour les groupes qui se plient/déplient
document.addEventListener('DOMContentLoaded', function() {
    const collapseElements = document.querySelectorAll('.collapse');
    collapseElements.forEach(function(element) {
        element.addEventListener('show.bs.collapse', function() {
            const header = document.querySelector('[data-bs-target="#' + element.id + '"]');
            header.setAttribute('aria-expanded', 'true');
        });

        element.addEventListener('hide.bs.collapse', function() {
            const header = document.querySelector('[data-bs-target="#' + element.id + '"]');
            header.setAttribute('aria-expanded', 'false');
        });
    });
});

// Édition en ligne
function makeEditable(element) {
    if (editingElements.has(element)) return;

    const currentText = element.textContent.trim();
    const field = element.dataset.field;
    const taskId = element.dataset.taskId;

    editingElements.add(element);

    // Créer un input
    const input = document.createElement(field === 'description' ? 'textarea' : 'input');
    input.value = currentText;
    input.className = 'form-control form-control-sm';
    if (field === 'description') {
        input.rows = 2;
    }

    // Remplacer le texte par l'input
    element.innerHTML = '';
    element.appendChild(input);
    input.focus();
    input.select();

    // Afficher le bouton de sauvegarde
    const row = element.closest('tr');
    const saveBtn = row.querySelector('.save-btn');
    if (saveBtn) {
        saveBtn.style.display = 'block';
    }

    // Sauvegarder en appuyant sur Entrée
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            saveTaskChanges(taskId);
        }
        if (e.key === 'Escape') {
            cancelEdit(element, currentText);
        }
    });

    // Sauvegarder en perdant le focus
    input.addEventListener('blur', function() {
        setTimeout(() => saveTaskChanges(taskId), 100);
    });
}

function saveTaskChanges(taskId) {
    const row = document.querySelector(`tr[data-task-id="${taskId}"]`);
    const titleElement = row.querySelector('.editable-title');
    const descElement = row.querySelector('.editable-description');

    let updates = {};
    let hasChanges = false;

    // Vérifier le titre
    if (editingElements.has(titleElement)) {
        const input = titleElement.querySelector('input');
        if (input) {
            updates.title = input.value.trim();
            hasChanges = true;
        }
    }

    // Vérifier la description
    if (editingElements.has(descElement)) {
        const textarea = descElement.querySelector('textarea');
        if (textarea) {
            updates.description = textarea.value.trim();
            hasChanges = true;
        }
    }

    if (!hasChanges) return;

    // Envoyer les mises à jour
    fetch(`/task/update_field/${taskId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Restaurer les textes
            if (updates.title) {
                titleElement.textContent = updates.title;
                editingElements.delete(titleElement);
            }
            if (updates.description) {
                descElement.textContent = updates.description.length > 50 ?
                    updates.description.substring(0, 50) + '...' : updates.description;
                editingElements.delete(descElement);
            }

            // Masquer le bouton de sauvegarde
            const saveBtn = row.querySelector('.save-btn');
            if (saveBtn) {
                saveBtn.style.display = 'none';
            }

            showNotification('Modifications sauvegardées', 'success');
        } else {
            showNotification('Erreur lors de la sauvegarde', 'error');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showNotification('Erreur de connexion', 'error');
    });
}

function cancelEdit(element, originalText) {
    element.textContent = originalText;
    editingElements.delete(element);

    const row = element.closest('tr');
    const saveBtn = row.querySelector('.save-btn');
    if (saveBtn) {
        saveBtn.style.display = 'none';
    }
}

// Mise à jour du statut
function updateTaskStatus(selectElement) {
    const taskId = selectElement.dataset.taskId;
    const newStatus = selectElement.value;

    fetch(`/task/update_field/${taskId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            field: 'status',
            value: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Statut mis à jour', 'success');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showNotification('Erreur lors de la mise à jour', 'error');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showNotification('Erreur de connexion', 'error');
    });
}

// Mise à jour de la date
function updateTaskDate(inputElement) {
    const taskId = inputElement.dataset.taskId;
    const newDate = inputElement.value;

    fetch(`/task/update_field/${taskId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            field: 'due_date',
            value: newDate
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Date mise à jour', 'success');
        } else {
            showNotification('Erreur lors de la mise à jour', 'error');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showNotification('Erreur de connexion', 'error');
    });
}

// Mise à jour de l'assignation
function updateTaskAssignee(selectElement) {
    const taskId = selectElement.dataset.taskId;
    const newAssignee = selectElement.value;

    fetch(`/task/update_field/${taskId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            field: 'assigned_to',
            value: newAssignee
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Assignation mise à jour', 'success');
        } else {
            showNotification('Erreur lors de la mise à jour', 'error');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showNotification('Erreur de connexion', 'error');
    });
}

// Envoi par WhatsApp
function sendTaskWhatsApp(taskId, taskTitle) {
    const message = `📋 Tâche: ${taskTitle}\n🔗 Lien: ${window.location.origin}/task/edit/${taskId}`;
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
}

// Envoi par Email
function sendTaskEmail(taskId, taskTitle) {
    const subject = `Tâche: ${taskTitle}`;
    const body = `Bonjour,\n\nVeuillez consulter la tâche suivante:\n\nTitre: ${taskTitle}\nLien: ${window.location.origin}/task/edit/${taskId}\n\nCordialement`;

    const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoUrl;
}

// Notifications
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; font-size: 12px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Fonctions pour la sidebar
function toggleFilter() {
    // Implémentation du filtrage
    showNotification('Fonction de filtrage à implémenter', 'info');
}

function toggleSort() {
    // Implémentation du tri
    showNotification('Fonction de tri à implémenter', 'info');
}

function toggleView(viewType) {
    // Implémentation du changement de vue
    showNotification(`Vue ${viewType} à implémenter`, 'info');
}
</script>
{% endblock %}
