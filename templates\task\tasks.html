
{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-tasks text-primary me-2"></i>
                        Gestion des Tâches
                    </h3>
                    <div class="btn-group">
                        <a href="{{ url_for('task.add_task') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Nouvelle Tâche
                        </a>
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-1"></i> Filtres
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="filterTasks('all')">Toutes les tâches</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterTasks('nouveau')">Nouvelles</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterTasks('en_cours')">En cours</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterTasks('termine')">Terminées</a></li>
                            <li><a class="dropdown-item" href="#" onclick="filterTasks('annule')">Annulées</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Barre de recherche et filtres -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" placeholder="Rechercher une tâche...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter">
                                <option value="">Tous les statuts</option>
                                <option value="nouveau">Nouveau</option>
                                <option value="en_cours">En cours</option>
                                <option value="termine">Terminé</option>
                                <option value="annule">Annulé</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="priorityFilter">
                                <option value="">Toutes les priorités</option>
                                <option value="basse">Basse</option>
                                <option value="normale">Normale</option>
                                <option value="haute">Haute</option>
                                <option value="urgente">Urgente</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                                <i class="fas fa-undo me-1"></i> Reset
                            </button>
                        </div>
                    </div>

                    <!-- Statistiques rapides -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4 class="mb-0">{{ tasks|selectattr('status', 'equalto', 'nouveau')|list|length }}</h4>
                                    <small>Nouvelles</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h4 class="mb-0">{{ tasks|selectattr('status', 'equalto', 'en_cours')|list|length }}</h4>
                                    <small>En cours</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4 class="mb-0">{{ tasks|selectattr('status', 'equalto', 'termine')|list|length }}</h4>
                                    <small>Terminées</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h4 class="mb-0">{{ tasks|length }}</h4>
                                    <small>Total</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Table des tâches -->
                    {% if tasks %}
                    <div class="table-responsive">
                        <table class="table table-hover" id="tasksTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th width="25%">Titre</th>
                                    <th width="15%">Statut</th>
                                    <th width="15%">Priorité</th>
                                    <th width="15%">Échéance</th>
                                    <th width="15%">Catégorie</th>
                                    <th width="10%">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in tasks %}
                                <tr data-status="{{ task.status }}" data-priority="{{ task.priority }}">
                                    <td>
                                        <input type="checkbox" class="form-check-input task-checkbox" value="{{ task.id }}">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <h6 class="mb-0">{{ task.title }}</h6>
                                                {% if task.description %}
                                                <small class="text-muted">{{ task.description[:50] }}{% if task.description|length > 50 %}...{% endif %}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge
                                            {% if task.status == 'nouveau' %}bg-primary
                                            {% elif task.status == 'en_cours' %}bg-warning
                                            {% elif task.status == 'termine' %}bg-success
                                            {% elif task.status == 'annule' %}bg-danger
                                            {% endif %}">
                                            {% if task.status == 'nouveau' %}Nouveau
                                            {% elif task.status == 'en_cours' %}En cours
                                            {% elif task.status == 'termine' %}Terminé
                                            {% elif task.status == 'annule' %}Annulé
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge
                                            {% if task.priority == 'basse' %}bg-secondary
                                            {% elif task.priority == 'normale' %}bg-info
                                            {% elif task.priority == 'haute' %}bg-warning
                                            {% elif task.priority == 'urgente' %}bg-danger
                                            {% endif %}">
                                            {% if task.priority == 'basse' %}Basse
                                            {% elif task.priority == 'normale' %}Normale
                                            {% elif task.priority == 'haute' %}Haute
                                            {% elif task.priority == 'urgente' %}Urgente
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        {% if task.due_date %}
                                            {% set today = moment().date() if moment is defined else none %}
                                            <span class="{% if today and task.due_date < today and task.status != 'termine' %}text-danger{% endif %}">
                                                {{ task.due_date.strftime('%d/%m/%Y') }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if task.category %}
                                            <span class="badge bg-light text-dark">{{ task.category }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('task.view_task', id=task.id) }}" class="btn btn-sm btn-outline-primary" title="Voir">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('task.edit_task', id=task.id) }}" class="btn btn-sm btn-outline-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('task.print_task', id=task.id) }}" class="btn btn-sm btn-outline-secondary" title="Imprimer" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" title="Supprimer" onclick="confirmDelete({{ task.id }}, '{{ task.title }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune tâche trouvée</h5>
                        <p class="text-muted">Commencez par ajouter votre première tâche.</p>
                        <a href="{{ url_for('task.add_task') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Ajouter une tâche
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la tâche <strong id="task-title"></strong> ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <a href="#" id="delete-link" class="btn btn-danger">Supprimer</a>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const priorityFilter = document.getElementById('priorityFilter');
    const selectAll = document.getElementById('selectAll');
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');

    // Fonction de filtrage
    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;
        const priorityValue = priorityFilter.value;
        const rows = document.querySelectorAll('#tasksTable tbody tr');

        rows.forEach(row => {
            const title = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
            const status = row.getAttribute('data-status');
            const priority = row.getAttribute('data-priority');

            const matchesSearch = title.includes(searchTerm);
            const matchesStatus = !statusValue || status === statusValue;
            const matchesPriority = !priorityValue || priority === priorityValue;

            if (matchesSearch && matchesStatus && matchesPriority) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // Sélection multiple
    selectAll.addEventListener('change', function() {
        taskCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Événements de filtrage
    searchInput.addEventListener('input', filterTable);
    statusFilter.addEventListener('change', filterTable);
    priorityFilter.addEventListener('change', filterTable);
});

// Filtres rapides
function filterTasks(status) {
    const statusFilter = document.getElementById('statusFilter');
    statusFilter.value = status === 'all' ? '' : status;
    statusFilter.dispatchEvent(new Event('change'));
}

// Reset des filtres
function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('priorityFilter').value = '';
    document.getElementById('searchInput').dispatchEvent(new Event('input'));
}

// Confirmation de suppression
function confirmDelete(taskId, taskTitle) {
    document.getElementById('task-title').textContent = taskTitle;
    document.getElementById('delete-link').href = "{{ url_for('task.delete_task', id=0) }}".replace('0', taskId);
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
