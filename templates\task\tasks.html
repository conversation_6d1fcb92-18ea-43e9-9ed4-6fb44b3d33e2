{% extends "base.html" %}

{% block title %}Gestion des tâches - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3"><i class="fas fa-tasks me-2"></i>Gestion des tâches</h1>
        <p class="text-muted">Gérez vos tâches et suivez leur progression.</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('task.add_task') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Ajouter une tâche
        </a>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Total</h6>
                        <h3 class="mb-0">{{ tasks|length }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tasks fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">En cours</h6>
                        <h3 class="mb-0">{{ tasks|selectattr('status', 'equalto', 'in_progress')|list|length }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Terminées</h6>
                        <h3 class="mb-0">{{ tasks|selectattr('status', 'equalto', 'completed')|list|length }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">En retard</h6>
                        <h3 class="mb-0">{{ tasks|selectattr('is_overdue')|list|length }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filtres -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <label for="status-filter" class="form-label">Filtrer par statut:</label>
                <select id="status-filter" class="form-select">
                    <option value="">Tous les statuts</option>
                    <option value="new">Nouvelle</option>
                    <option value="in_progress">En cours</option>
                    <option value="completed">Terminée</option>
                    <option value="cancelled">Annulée</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="priority-filter" class="form-label">Filtrer par priorité:</label>
                <select id="priority-filter" class="form-select">
                    <option value="">Toutes les priorités</option>
                    <option value="low">Faible</option>
                    <option value="normal">Normale</option>
                    <option value="high">Élevée</option>
                    <option value="urgent">Urgente</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="search-input" class="form-label">Rechercher:</label>
                <input type="text" id="search-input" class="form-control" placeholder="Rechercher dans les tâches...">
            </div>
        </div>
    </div>
</div>

<!-- Liste des tâches -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Liste des tâches</h5>
    </div>
    <div class="card-body">
        {% if tasks %}
        <div class="table-responsive">
            <table class="table table-striped" id="tasks-table">
                <thead>
                    <tr>
                        <th>Titre</th>
                        <th>Description</th>
                        <th>Statut</th>
                        <th>Priorité</th>
                        <th>Date d'échéance</th>
                        <th>Créée le</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for task in tasks %}
                    <tr data-status="{{ task.status }}" data-priority="{{ task.priority }}">
                        <td>
                            <strong>{{ task.title }}</strong>
                        </td>
                        <td>
                            <span class="text-muted">
                                {{ task.description[:50] + '...' if task.description and task.description|length > 50 else task.description or 'Aucune description' }}
                            </span>
                        </td>
                        <td>
                            {% if task.status == 'new' %}
                                <span class="badge bg-secondary">Nouvelle</span>
                            {% elif task.status == 'in_progress' %}
                                <span class="badge bg-warning">En cours</span>
                            {% elif task.status == 'completed' %}
                                <span class="badge bg-success">Terminée</span>
                            {% elif task.status == 'cancelled' %}
                                <span class="badge bg-danger">Annulée</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if task.priority == 'low' %}
                                <span class="badge bg-light text-dark">Faible</span>
                            {% elif task.priority == 'normal' %}
                                <span class="badge bg-info">Normale</span>
                            {% elif task.priority == 'high' %}
                                <span class="badge bg-warning">Élevée</span>
                            {% elif task.priority == 'urgent' %}
                                <span class="badge bg-danger">Urgente</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if task.due_date %}
                                {{ task.due_date.strftime('%d/%m/%Y') }}
                                {% if task.is_overdue %}
                                    <i class="fas fa-exclamation-triangle text-danger ms-1" title="En retard"></i>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">Non définie</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if task.created_at %}
                                {{ task.created_at.strftime('%d/%m/%Y') }}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('task.view_task', id=task.id) }}" class="btn btn-sm btn-outline-info" title="Voir">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('task.edit_task', id=task.id) }}" class="btn btn-sm btn-outline-warning" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('task.print_task', id=task.id) }}" class="btn btn-sm btn-outline-secondary" title="Imprimer" target="_blank">
                                    <i class="fas fa-print"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger" title="Supprimer" onclick="confirmDelete({{ task.id }}, '{{ task.title }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucune tâche trouvée</h5>
            <p class="text-muted">Commencez par ajouter votre première tâche.</p>
            <a href="{{ url_for('task.add_task') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Ajouter une tâche
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la tâche <strong id="task-title"></strong> ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <a href="#" id="delete-link" class="btn btn-danger">Supprimer</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Filtrage et recherche
document.addEventListener('DOMContentLoaded', function() {
    const statusFilter = document.getElementById('status-filter');
    const priorityFilter = document.getElementById('priority-filter');
    const searchInput = document.getElementById('search-input');
    const table = document.getElementById('tasks-table');
    
    function filterTable() {
        const statusValue = statusFilter.value.toLowerCase();
        const priorityValue = priorityFilter.value.toLowerCase();
        const searchValue = searchInput.value.toLowerCase();
        const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
        
        for (let row of rows) {
            const status = row.getAttribute('data-status');
            const priority = row.getAttribute('data-priority');
            const text = row.textContent.toLowerCase();
            
            const statusMatch = !statusValue || status === statusValue;
            const priorityMatch = !priorityValue || priority === priorityValue;
            const searchMatch = !searchValue || text.includes(searchValue);
            
            row.style.display = statusMatch && priorityMatch && searchMatch ? '' : 'none';
        }
    }
    
    statusFilter.addEventListener('change', filterTable);
    priorityFilter.addEventListener('change', filterTable);
    searchInput.addEventListener('input', filterTable);
});

// Confirmation de suppression
function confirmDelete(taskId, taskTitle) {
    document.getElementById('task-title').textContent = taskTitle;
    document.getElementById('delete-link').href = "{{ url_for('task.delete_task', id=0) }}".replace('0', taskId);
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
