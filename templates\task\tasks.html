{% extends "base.html" %}

{% block title %}Gestion des tâches - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3"><i class="fas fa-tasks me-2"></i>Gestion des tâches</h1>
        <p class="text-muted">Gérez les tâches et assignez-les aux employés.</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('task.add_task') }}" class="btn btn-primary me-2">
            <i class="fas fa-plus me-1"></i> Ajouter une tâche
        </a>
        <a href="{{ url_for('task.assign_task') }}" class="btn btn-success">
            <i class="fas fa-user-check me-1"></i> Assigner une tâche
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Liste des tâches</h5>
                    </div>
                    <div class="col-md-6">
                        <input type="text" id="table-filter" class="form-control" placeholder="Rechercher une tâche...">
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped filterable-table">
                        <thead>
                            <tr>
                                <th>Titre</th>
                                <th>Description</th>
                                <th>Date de création</th>
                                <th>Pièce jointe</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in tasks %}
                            <tr>
                                <td>{{ task.title }}</td>
                                <td>{{ task.description|truncate(50) }}</td>
                                <td>{{ task.created_at.strftime('%d/%m/%Y') }}</td>
                                <td>
                                    {% if task.attachment_path %}
                                    <a href="{{ url_for('task.download_file', filename=task.attachment_path.split('/')[-1]) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-download me-1"></i> Télécharger
                                    </a>
                                    {% else %}
                                    <span class="text-muted">Aucun fichier</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('task.edit_task', id=task.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ url_for('task.delete_task', id=task.id) }}" method="POST" class="d-inline">
                                            <button type="submit" class="btn btn-sm btn-danger delete-confirm" data-bs-toggle="tooltip" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        <button type="button" class="btn btn-sm btn-info print-item" data-bs-toggle="tooltip" title="Imprimer" onclick="printTaskInfo({{ task.id }}, '{{ task.title }}', '{{ task.description|replace('\n', ' ')|replace('\r', ' ') }}', '{{ task.created_at.strftime('%d/%m/%Y') }}')">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">Aucune tâche trouvée</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-calendar-day me-2"></i>Tâches quotidiennes</h5>
                <a href="{{ url_for('task.daily_tasks') }}" class="btn btn-sm btn-primary">Voir tout</a>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> Consultez les tâches quotidiennes pour voir les tâches assignées aux employés et suivre leur progression.
                </div>
                <div class="text-center">
                    <a href="{{ url_for('task.daily_tasks') }}" class="btn btn-primary me-2">
                        <i class="fas fa-calendar-day me-1"></i> Voir les tâches quotidiennes
                    </a>
                    <a href="{{ url_for('task.task_checkboxes') }}" class="btn btn-success">
                        <i class="fas fa-tasks me-1"></i> Liste des 50 tâches
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function printTaskInfo(id, title, description, createdAt) {
        // Créer une fenêtre d'impression
        let printWindow = window.open('', '_blank');

        // Contenu HTML à imprimer
        let content = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Fiche Tâche - ${title}</title>
                <style>
                    body { font-family: Arial, sans-serif; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .info-table { width: 100%; border-collapse: collapse; }
                    .info-table th, .info-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    .info-table th { background-color: #f2f2f2; width: 30%; }
                    .footer { margin-top: 30px; text-align: center; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Fiche Tâche</h1>
                    <p>Date d'impression: ${new Date().toLocaleDateString()}</p>
                </div>

                <table class="info-table">
                    <tr>
                        <th>ID</th>
                        <td>${id}</td>
                    </tr>
                    <tr>
                        <th>Titre</th>
                        <td>${title}</td>
                    </tr>
                    <tr>
                        <th>Description</th>
                        <td>${description}</td>
                    </tr>
                    <tr>
                        <th>Date de création</th>
                        <td>${createdAt}</td>
                    </tr>
                </table>

                <div class="footer">
                    <p>Gestion des Pointages - Document généré automatiquement</p>
                </div>
            </body>
            </html>
        `;

        // Écrire le contenu dans la fenêtre d'impression
        printWindow.document.open();
        printWindow.document.write(content);
        printWindow.document.close();

        // Imprimer après le chargement du contenu
        printWindow.onload = function() {
            printWindow.print();
        };
    }
</script>
{% endblock %}