
{% extends "base.html" %}
{% block title %}Liste des tâches{% endblock %}
{% block content %}

{% for group in groups %}
<div class="mb-4">
  <h5 class="text-{{ group.color }}">
    <i class="bi bi-chevron-down"></i> {{ group.title }}
  </h5>
  <table class="table table-bordered table-hover">
    <thead class="table-light">
      <tr>
        <th></th>
        <th>Élément</th>
        <th>Personne</th>
        <th>Statut</th>
        <th>Période</th>
        <th>Échéance</th>
        <th><PERSON>u déroulant</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {% for task in group.tasks %}
      <tr>
        <td><input type="checkbox" class="task-check" data-id="{{ task.id }}"></td>
        <td>{{ task.title }}</td>
        <td>{{ task.assigned_user.username if task.assigned_user else '' }}</td>
        <td>
          <span class="badge 
            {% if task.status == 'En cours' %}bg-warning text-dark
            {% elif task.status == 'Bloqué' %}bg-danger
            {% elif task.status == 'Terminé' %}bg-success
            {% else %}bg-secondary{% endif %}">
            {{ task.status }}
          </span>
        </td>
        <td>
          {% if task.period_start and task.period_end %}
            <span class="badge bg-primary">{{ task.period_start.strftime('%b %d') }} - {{ task.period_end.strftime('%b %d') }}</span>
          {% endif %}
        </td>
        <td>
          {% if task.deadline %}
            <span class="badge bg-dark">{{ task.deadline.strftime('%b %d') }}</span>
          {% else %}
            <span class="badge bg-secondary">-</span>
          {% endif %}
        </td>
        <td>{{ task.dropdown_info or '' }}</td>
        <td>
          <a class="btn btn-sm btn-success" href="https://wa.me/{{ task.assigned_user.phone }}?text=Tâche: {{ task.title }}" target="_blank">WhatsApp</a>
          <a class="btn btn-sm btn-primary" href="mailto:{{ task.assigned_user.email }}?subject=Tâche: {{ task.title }}&body=Statut: {{ task.status }}" target="_blank">Email</a>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endfor %}

{% endblock %}
