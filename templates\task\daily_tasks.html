{% extends "base.html" %}

{% block title %}Tâches quotidiennes - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3"><i class="fas fa-calendar-day me-2"></i>Tâches quotidiennes</h1>
        <p class="text-muted">Suivez les tâches assignées aux employés pour la journée.</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('task.assign_task') }}" class="btn btn-primary">
            <i class="fas fa-user-check me-1"></i> Assigner une tâche
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0"><i class="fas fa-calendar-day me-2"></i>Tâches du {{ selected_date.strftime('%d/%m/%Y') }}</h5>
                    </div>
                    <div class="col-md-6 text-end">
                        <form class="d-inline-flex" method="GET" action="{{ url_for('task.daily_tasks') }}">
                            <input type="date" name="date" class="form-control me-2" value="{{ selected_date.isoformat() }}">
                            <button type="submit" class="btn btn-primary">Afficher</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if employee_tasks %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Employé</th>
                                <th>Tâche</th>
                                <th>Statut</th>
                                <th>Notes</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in employee_tasks %}
                            <tr>
                                <td>{{ task.employee.full_name }}</td>
                                <td>{{ task.task.title }}</td>
                                <td>
                                    {% if task.completed %}
                                    <span class="badge bg-success">Terminée</span>
                                    {% else %}
                                    <span class="badge bg-warning">En cours</span>
                                    {% endif %}
                                </td>
                                <td>{{ task.notes or '-' }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        {% if not task.completed %}
                                        <button class="btn btn-sm btn-success complete-task" data-task-id="{{ task.id }}">
                                            <i class="fas fa-check"></i> Marquer comme terminée
                                        </button>
                                        {% else %}
                                        <span class="text-muted me-2">Terminée le {{ task.completion_date.strftime('%d/%m/%Y %H:%M') }}</span>
                                        {% endif %}

                                        <button type="button" class="btn btn-sm btn-primary" onclick="window.location.href='{{ url_for('task.edit_task', id=task.task.id) }}'">
                                            <i class="fas fa-edit"></i>
                                        </button>

                                        <a href="{{ url_for('task.delete_task', id=task.task.id) }}" class="btn btn-sm btn-danger delete-confirm" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette tâche?')">
                                            <i class="fas fa-trash"></i>
                                        </a>

                                        <button type="button" class="btn btn-sm btn-info print-item" onclick="printDailyTaskInfo({{ task.id }}, '{{ task.employee.full_name }}', '{{ task.task.title }}', '{{ task.date.strftime('%d/%m/%Y') }}', '{{ 'Terminée' if task.completed else 'En cours' }}', '{{ task.notes or '-' }}')">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> Aucune tâche n'a été assignée pour cette date.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Marquer une tâche comme terminée
        document.querySelectorAll('.complete-task').forEach(button => {
            button.addEventListener('click', function() {
                const taskId = this.getAttribute('data-task-id');

                fetch(`/task/complete/${taskId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Recharger la page pour afficher les changements
                        window.location.reload();
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    alert('Une erreur est survenue. Veuillez réessayer.');
                });
            });
        });
    });

    function printDailyTaskInfo(id, employeeName, taskTitle, taskDate, status, notes) {
        // Créer une fenêtre d'impression
        let printWindow = window.open('', '_blank');

        // Contenu HTML à imprimer
        let content = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Fiche Tâche Quotidienne</title>
                <style>
                    body { font-family: Arial, sans-serif; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .info-table { width: 100%; border-collapse: collapse; }
                    .info-table th, .info-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    .info-table th { background-color: #f2f2f2; width: 30%; }
                    .footer { margin-top: 30px; text-align: center; font-size: 12px; }
                    .status-completed { color: green; font-weight: bold; }
                    .status-pending { color: orange; font-weight: bold; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Fiche Tâche Quotidienne</h1>
                    <p>Date d'impression: ${new Date().toLocaleDateString()}</p>
                </div>

                <table class="info-table">
                    <tr>
                        <th>ID</th>
                        <td>${id}</td>
                    </tr>
                    <tr>
                        <th>Employé</th>
                        <td>${employeeName}</td>
                    </tr>
                    <tr>
                        <th>Tâche</th>
                        <td>${taskTitle}</td>
                    </tr>
                    <tr>
                        <th>Date</th>
                        <td>${taskDate}</td>
                    </tr>
                    <tr>
                        <th>Statut</th>
                        <td class="${status === 'Terminée' ? 'status-completed' : 'status-pending'}">${status}</td>
                    </tr>
                    <tr>
                        <th>Notes</th>
                        <td>${notes}</td>
                    </tr>
                </table>

                <div class="footer">
                    <p>Gestion des Pointages - Document généré automatiquement</p>
                </div>
            </body>
            </html>
        `;

        // Écrire le contenu dans la fenêtre d'impression
        printWindow.document.open();
        printWindow.document.write(content);
        printWindow.document.close();

        // Imprimer après le chargement du contenu
        printWindow.onload = function() {
            printWindow.print();
        };
    }
</script>
{% endblock %}
