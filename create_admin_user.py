#!/usr/bin/env python3
"""
Script pour créer un utilisateur administrateur
"""

import sqlite3
import os
from werkzeug.security import generate_password_hash
from datetime import datetime

def create_admin_user():
    """Créer un utilisateur administrateur"""
    
    # Chemin vers la base de données
    db_path = 'instance/app.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Base de données non trouvée: {db_path}")
        return False
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vérifier si l'utilisateur admin existe déjà
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        existing_admin = cursor.fetchone()
        
        if existing_admin:
            print("ℹ️ L'utilisateur admin existe déjà")
            # Mettre à jour le mot de passe
            password_hash = generate_password_hash('admin')
            cursor.execute("UPDATE users SET password_hash = ? WHERE username = 'admin'", (password_hash,))
            print("✅ Mot de passe admin mis à jour")
        else:
            # Créer l'utilisateur admin
            password_hash = generate_password_hash('admin')
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, is_admin, can_manage_tasks, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                'admin',
                '<EMAIL>',
                password_hash,
                1,  # is_admin = True
                1,  # can_manage_tasks = True
                datetime.now().isoformat()
            ))
            print("✅ Utilisateur admin créé")
        
        conn.commit()
        
        # Vérifier la création
        cursor.execute("SELECT id, username, email, is_admin, can_manage_tasks FROM users WHERE username = 'admin'")
        admin_user = cursor.fetchone()
        
        if admin_user:
            print(f"✅ Utilisateur admin confirmé:")
            print(f"  - ID: {admin_user[0]}")
            print(f"  - Username: {admin_user[1]}")
            print(f"  - Email: {admin_user[2]}")
            print(f"  - Is Admin: {admin_user[3]}")
            print(f"  - Can Manage Tasks: {admin_user[4]}")
            print(f"  - Mot de passe: admin")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création de l'utilisateur admin: {e}")
        if 'conn' in locals():
            conn.close()
        return False

if __name__ == "__main__":
    print("🔧 Création de l'utilisateur administrateur...")
    success = create_admin_user()
    
    if success:
        print("🎉 Utilisateur admin créé avec succès!")
        print("🌐 Connectez-vous avec: admin / admin")
        print("🌐 URL: http://localhost:5000/login")
    else:
        print("💥 Création de l'utilisateur admin échouée!")
