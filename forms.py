from flask_wtf import F<PERSON>kForm
from flask_wtf.file import <PERSON><PERSON>ield, FileAllowed
from wtforms import <PERSON><PERSON>ield, PasswordField, BooleanField, SubmitField, TextAreaField, FloatField, DateField, SelectField, HiddenField, IntegerField
from wtforms.validators import DataRequired, Email, EqualTo, Length, Optional, ValidationError, NumberRange
from models import User, Employee
from datetime import date

class LoginForm(FlaskForm):
    """Formulaire de connexion"""
    username = StringField('Nom d\'utilisateur', validators=[DataRequired()])
    password = PasswordField('Mot de passe', validators=[DataRequired()])
    remember_me = BooleanField('Se souvenir de moi')
    submit = SubmitField('Se connecter')

class RegistrationForm(FlaskForm):
    """Formulaire d'inscription"""
    username = StringField('Nom d\'utilisateur', validators=[DataRequired(), Length(min=3, max=64)])
    email = <PERSON>Field('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Mot de passe', validators=[DataRequired(), Length(min=6)])
    password2 = PasswordField('Répéter le mot de passe', validators=[DataRequired(), EqualTo('password')])

    # Permissions
    is_admin = BooleanField('Administrateur')
    can_manage_users = BooleanField('Gestion des utilisateurs')
    can_manage_employees = BooleanField('Gestion des employés')
    can_manage_tasks = BooleanField('Gestion des tâches')
    can_manage_finances = BooleanField('Gestion des finances')
    can_access_registration_files = BooleanField('Accès aux fiches d\'inscription')
    can_access_technical_files = BooleanField('Accès aux dossiers techniques')
    can_access_reimbursement_files = BooleanField('Accès aux dossiers de remboursement')
    can_access_organization_files = BooleanField('Accès aux fichiers d\'organisme')
    can_access_trainer_files = BooleanField('Accès aux fichiers de formateur')
    can_access_trainer_schedule = BooleanField('Accès à l\'agenda des formateurs')

    submit = SubmitField('Enregistrer')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('Ce nom d\'utilisateur est déjà utilisé. Veuillez en choisir un autre.')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('Cet email est déjà utilisé. Veuillez en choisir un autre.')

class UserEditForm(FlaskForm):
    """Formulaire de modification d'utilisateur"""
    username = StringField('Nom d\'utilisateur', validators=[DataRequired(), Length(min=3, max=64)])
    email = StringField('Email', validators=[DataRequired(), Email()])

    # Permissions
    is_admin = BooleanField('Administrateur')
    can_manage_users = BooleanField('Gestion des utilisateurs')
    can_manage_employees = BooleanField('Gestion des employés')
    can_manage_tasks = BooleanField('Gestion des tâches')
    can_manage_finances = BooleanField('Gestion des finances')
    can_access_registration_files = BooleanField('Accès aux fiches d\'inscription')
    can_access_technical_files = BooleanField('Accès aux dossiers techniques')
    can_access_reimbursement_files = BooleanField('Accès aux dossiers de remboursement')
    can_access_organization_files = BooleanField('Accès aux fichiers d\'organisme')
    can_access_trainer_files = BooleanField('Accès aux fichiers de formateur')
    can_access_trainer_schedule = BooleanField('Accès à l\'agenda des formateurs')

    submit = SubmitField('Mettre à jour')

class EmployeeForm(FlaskForm):
    """Formulaire d'ajout/modification d'employé"""
    first_name = StringField('Prénom', validators=[DataRequired(), Length(max=64)])
    last_name = StringField('Nom', validators=[DataRequired(), Length(max=64)])
    position = StringField('Poste', validators=[DataRequired(), Length(max=64)])
    cin = StringField('CIN', validators=[DataRequired(), Length(max=20)])
    cnss = StringField('CNSS', validators=[Optional(), Length(max=20)])
    phone = StringField('Téléphone', validators=[Optional(), Length(max=20)])
    email = StringField('Email', validators=[Optional(), Email(), Length(max=120)])
    daily_rate = FloatField('Prix par jour (MAD)', validators=[DataRequired()])
    hire_date = DateField('Date d\'embauche', validators=[Optional()], default=date.today)
    notes = TextAreaField('Notes', validators=[Optional()])
    submit = SubmitField('Enregistrer')

    def validate_cin(self, cin):
        employee = Employee.query.filter_by(cin=cin.data).first()
        if employee and (not hasattr(self, 'id') or employee.id != self.id.data):
            raise ValidationError('Ce numéro CIN est déjà utilisé par un autre employé.')

    def validate_cnss(self, cnss):
        if cnss.data:
            employee = Employee.query.filter_by(cnss=cnss.data).first()
            if employee and (not hasattr(self, 'id') or employee.id != self.id.data):
                raise ValidationError('Ce numéro CNSS est déjà utilisé par un autre employé.')

    def validate_email(self, email):
        if email.data:
            employee = Employee.query.filter_by(email=email.data).first()
            if employee and (not hasattr(self, 'id') or employee.id != self.id.data):
                raise ValidationError('Cet email est déjà utilisé par un autre employé.')

class TaskForm(FlaskForm):
    """Formulaire d'ajout/modification de tâche"""
    title = StringField('Titre', validators=[DataRequired(), Length(max=128)])
    description = TextAreaField('Description', validators=[Optional()])

    # Champs avancés
    priority = SelectField('Priorité', choices=[
        (0, 'Basse'),
        (1, 'Normale'),
        (2, 'Haute'),
        (3, 'Urgente')
    ], coerce=int, default=1)

    status = SelectField('Statut', choices=[
        ('pending', 'En attente'),
        ('in_progress', 'En cours'),
        ('completed', 'Terminée'),
        ('cancelled', 'Annulée'),
        ('delayed', 'Retardée')
    ], default='pending')

    due_date = DateField('Date d\'échéance', validators=[Optional()])
    category = StringField('Catégorie', validators=[Optional(), Length(max=50)])
    tags = StringField('Tags (séparés par des virgules)', validators=[Optional()])
    color = StringField('Couleur', validators=[Optional(), Length(max=20)], default='#3498db')

    # Assignation
    assigned_to = SelectField('Assigné à', coerce=int, validators=[Optional()], default=0)
    employee_id = SelectField('Employé (optionnel)', coerce=int, validators=[Optional()], default=0)
    start_date = DateField('Date de début', validators=[Optional()])
    end_date = DateField('Date de fin', validators=[Optional()])

    # Pièce jointe
    attachment = FileField('Pièce jointe', validators=[
        Optional(),
        FileAllowed(['jpg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx'], 'Images, PDF ou documents Office uniquement!')
    ])

    submit = SubmitField('Enregistrer')

    def __init__(self, *args, **kwargs):
        super(TaskForm, self).__init__(*args, **kwargs)
        from models import Employee
        # Ajouter une option vide pour aucun employé
        self.employee_id.choices = [(0, 'Aucun employé sélectionné')] + [(e.id, e.full_name) for e in Employee.query.filter_by(active=True).all()]

class EmployeeTaskForm(FlaskForm):
    """Formulaire d'assignation de tâche à un employé"""
    employee_id = SelectField('Employé', coerce=int, validators=[DataRequired()])
    task_id = SelectField('Tâche', coerce=int, validators=[DataRequired()])
    date = DateField('Date d\'assignation', validators=[DataRequired()], default=date.today)

    # Champs avancés
    start_date = DateField('Date de début', validators=[Optional()])
    end_date = DateField('Date de fin', validators=[Optional()])

    priority = SelectField('Priorité', choices=[
        (0, 'Basse'),
        (1, 'Normale'),
        (2, 'Haute'),
        (3, 'Urgente')
    ], coerce=int, default=1)

    status = SelectField('Statut', choices=[
        ('pending', 'En attente'),
        ('in_progress', 'En cours'),
        ('completed', 'Terminée'),
        ('cancelled', 'Annulée'),
        ('delayed', 'Retardée')
    ], default='pending')

    progress = IntegerField('Progression (%)', validators=[Optional(), NumberRange(min=0, max=100)], default=0)
    notes = TextAreaField('Notes', validators=[Optional()])

    attachment = FileField('Pièce jointe', validators=[
        Optional(),
        FileAllowed(['jpg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx'], 'Images, PDF ou documents Office uniquement!')
    ])

    submit = SubmitField('Assigner')

class AttendanceForm(FlaskForm):
    """Formulaire de présence"""
    employee_id = SelectField('Employé', coerce=int, validators=[DataRequired()])
    date = DateField('Date', validators=[DataRequired()], default=date.today)
    present = BooleanField('Présent')
    notes = TextAreaField('Notes', validators=[Optional()])
    attachment = FileField('Pièce jointe', validators=[
        Optional(),
        FileAllowed(['jpg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx'], 'Images, PDF ou documents Office uniquement!')
    ])
    submit = SubmitField('Enregistrer')

class TransactionForm(FlaskForm):
    """Formulaire de transaction financière"""
    date = DateField('Date', validators=[DataRequired()], default=date.today)
    description = TextAreaField('Description', validators=[DataRequired()])
    amount = FloatField('Montant (MAD)', validators=[DataRequired()])
    transaction_type = SelectField('Type', choices=[('encaissement', 'Encaissement'), ('decaissement', 'Décaissement')], validators=[DataRequired()])
    attachment = FileField('Pièce jointe', validators=[
        Optional(),
        FileAllowed(['jpg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx'], 'Images, PDF ou documents Office uniquement!')
    ])
    submit = SubmitField('Enregistrer')
