"""
Migration pour créer les tables de l'entreprise et des sauvegardes
"""
import sqlite3
import os
from datetime import datetime

def create_company_tables():
    """Créer les tables pour les informations de l'entreprise et les sauvegardes"""
    
    # Chemin vers la base de données
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'app.db')
    
    # Connexion à la base de données
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Table company_info
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS company_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom_entreprise VARCHAR(200) NOT NULL,
                raison_sociale VARCHAR(200),
                secteur_activite VARCHAR(100),
                forme_juridique VARCHAR(50),
                adresse TEXT,
                ville VARCHAR(100),
                code_postal VARCHAR(20),
                pays VARCHAR(50) DEFAULT 'Maroc',
                telephone VARCHAR(20),
                fax VARCHAR(20),
                email VARCHAR(120),
                site_web VARCHAR(200),
                numero_registre_commerce VARCHAR(50),
                numero_ice VARCHAR(50),
                numero_if VARCHAR(50),
                numero_cnss VARCHAR(50),
                numero_patente VARCHAR(50),
                capital_social FLOAT,
                devise_capital VARCHAR(10) DEFAULT 'MAD',
                banque_principale VARCHAR(100),
                numero_compte_bancaire VARCHAR(50),
                rib VARCHAR(50),
                logo_path VARCHAR(200),
                signature_path VARCHAR(200),
                cachet_path VARCHAR(200),
                pied_de_page TEXT,
                mentions_legales TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Table database_backups
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS database_backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom_fichier VARCHAR(200) NOT NULL,
                chemin_fichier VARCHAR(500) NOT NULL,
                taille_fichier BIGINT,
                type_sauvegarde VARCHAR(20) DEFAULT 'manuel',
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (created_by) REFERENCES user (id)
            )
        ''')
        
        # Table auto_backup_settings
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS auto_backup_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                is_enabled BOOLEAN DEFAULT 0,
                frequence VARCHAR(20) DEFAULT 'daily',
                heure_execution TIME DEFAULT '02:00:00',
                jour_semaine INTEGER,
                jour_mois INTEGER,
                nombre_max_sauvegardes INTEGER DEFAULT 30,
                supprimer_anciennes BOOLEAN DEFAULT 1,
                dossier_sauvegarde VARCHAR(500),
                inclure_uploads BOOLEAN DEFAULT 1,
                compression BOOLEAN DEFAULT 1,
                email_notification BOOLEAN DEFAULT 0,
                email_destinataire VARCHAR(120),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_backup DATETIME
            )
        ''')
        
        # Insérer des paramètres par défaut pour la sauvegarde automatique
        cursor.execute('''
            INSERT OR IGNORE INTO auto_backup_settings (id, is_enabled, frequence, heure_execution)
            VALUES (1, 0, 'daily', '02:00:00')
        ''')
        
        # Créer les dossiers nécessaires
        upload_folder = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'uploads')
        company_folder = os.path.join(upload_folder, 'company')
        backup_folder = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backups')
        
        os.makedirs(company_folder, exist_ok=True)
        os.makedirs(backup_folder, exist_ok=True)
        
        # Valider les changements
        conn.commit()
        print("✅ Tables de l'entreprise créées avec succès!")
        print("✅ Dossiers créés:")
        print(f"   - {company_folder}")
        print(f"   - {backup_folder}")
        
    except Exception as e:
        conn.rollback()
        print(f"❌ Erreur lors de la création des tables: {e}")
        raise
    
    finally:
        conn.close()

def check_tables_exist():
    """Vérifier si les tables existent déjà"""
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'app.db')
    
    if not os.path.exists(db_path):
        print("❌ Base de données non trouvée!")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Vérifier l'existence des tables
        tables_to_check = ['company_info', 'database_backups', 'auto_backup_settings']
        existing_tables = []
        
        for table in tables_to_check:
            cursor.execute('''
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            ''', (table,))
            
            if cursor.fetchone():
                existing_tables.append(table)
        
        if existing_tables:
            print(f"ℹ️  Tables existantes: {', '.join(existing_tables)}")
        
        return len(existing_tables) == len(tables_to_check)
        
    finally:
        conn.close()

if __name__ == '__main__':
    print("🚀 Migration des tables de l'entreprise...")
    print("-" * 50)
    
    # Vérifier l'état actuel
    if check_tables_exist():
        print("ℹ️  Toutes les tables existent déjà.")
        response = input("Voulez-vous recréer les tables ? (y/N): ")
        if response.lower() != 'y':
            print("Migration annulée.")
            exit()
    
    # Créer les tables
    create_company_tables()
    
    print("-" * 50)
    print("✅ Migration terminée avec succès!")
    print("\n📋 Prochaines étapes:")
    print("1. Redémarrer l'application")
    print("2. Aller dans Entreprise > Informations pour configurer votre entreprise")
    print("3. Configurer les sauvegardes automatiques si nécessaire")
