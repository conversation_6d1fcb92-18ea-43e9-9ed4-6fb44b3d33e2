{% extends "base.html" %}

{% block title %}Assigner une tâche - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3"><i class="fas fa-user-check me-2"></i>Assigner une tâche</h1>
        <p class="text-muted">Assignez une tâche à un employé pour une date spécifique.</p>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-check me-2"></i>Formulaire d'assignation de tâche</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('task.assign_task') }}">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        <label for="employee_id" class="form-label">{{ form.employee_id.label }}</label>
                        {{ form.employee_id(class="form-control") }}
                        {% for error in form.employee_id.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="task_id" class="form-label">{{ form.task_id.label }}</label>
                        {{ form.task_id(class="form-control") }}
                        {% for error in form.task_id.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="date" class="form-label">{{ form.date.label }}</label>
                        {{ form.date(class="form-control", type="date") }}
                        {% for error in form.date.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">{{ form.notes.label }}</label>
                        {{ form.notes(class="form-control", rows=3, placeholder="Notes ou instructions spécifiques (optionnel)") }}
                        {% for error in form.notes.errors %}
                            <div class="text-danger">{{ error }}</div>
                        {% endfor %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{{ url_for('task.tasks') }}" class="btn btn-secondary me-md-2">Annuler</a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
