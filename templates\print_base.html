<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Document{% endblock %} - {{ company.nom_entreprise if company else 'Gestion des Pointages' }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Styles pour l'impression -->
    <style>
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                font-size: 12px;
                line-height: 1.4;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .page-break-avoid {
                page-break-inside: avoid;
            }
        }
        
        /* Styles généraux */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #fff;
        }
        
        .print-header {
            border-bottom: 2px solid #0d6efd;
            margin-bottom: 30px;
            padding-bottom: 20px;
        }
        
        .print-footer {
            border-top: 1px solid #dee2e6;
            margin-top: 30px;
            padding-top: 15px;
            font-size: 0.9em;
            color: #6c757d;
        }
        
        .company-logo {
            max-height: 80px;
            max-width: 200px;
        }
        
        .company-info {
            font-size: 0.9em;
            color: #495057;
        }
        
        .document-title {
            color: #0d6efd;
            border-bottom: 1px solid #0d6efd;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .signature-section {
            margin-top: 50px;
            text-align: right;
        }
        
        .signature-box {
            border: 1px solid #dee2e6;
            padding: 20px;
            margin-top: 20px;
            min-height: 100px;
            background-color: #f8f9fa;
        }
        
        .print-date {
            font-size: 0.8em;
            color: #6c757d;
            text-align: right;
            margin-bottom: 20px;
        }
        
        {% block print_styles %}{% endblock %}
    </style>
</head>
<body>
    <!-- Boutons d'impression (masqués à l'impression) -->
    <div class="no-print mb-3">
        <div class="d-flex justify-content-between align-items-center">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print me-1"></i> Imprimer
            </button>
            <button onclick="window.close()" class="btn btn-secondary">
                <i class="fas fa-times me-1"></i> Fermer
            </button>
        </div>
        <hr>
    </div>
    
    <!-- En-tête du document -->
    <div class="print-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                {% if company %}
                <div class="company-info">
                    <h2 class="mb-1">{{ company.nom_entreprise }}</h2>
                    {% if company.raison_sociale %}
                    <p class="mb-1"><strong>{{ company.raison_sociale }}</strong></p>
                    {% endif %}
                    
                    {% if company.adresse or company.ville %}
                    <p class="mb-1">
                        {% if company.adresse %}{{ company.adresse }}{% endif %}
                        {% if company.ville %}
                            {% if company.adresse %}, {% endif %}{{ company.ville }}
                        {% endif %}
                        {% if company.code_postal %} {{ company.code_postal }}{% endif %}
                    </p>
                    {% endif %}
                    
                    <div class="row">
                        {% if company.telephone %}
                        <div class="col-auto">
                            <i class="fas fa-phone me-1"></i> {{ company.telephone }}
                        </div>
                        {% endif %}
                        {% if company.email %}
                        <div class="col-auto">
                            <i class="fas fa-envelope me-1"></i> {{ company.email }}
                        </div>
                        {% endif %}
                        {% if company.site_web %}
                        <div class="col-auto">
                            <i class="fas fa-globe me-1"></i> {{ company.site_web }}
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% else %}
                <h2>Gestion des Pointages</h2>
                {% endif %}
            </div>
            <div class="col-md-4 text-end">
                {% if company and company.logo_path %}
                <img src="{{ url_for('static', filename='uploads/' + company.logo_path) }}" 
                     alt="Logo" class="company-logo">
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Date d'impression -->
    <div class="print-date">
        Imprimé le {{ moment().format('DD/MM/YYYY à HH:mm') }}
    </div>
    
    <!-- Titre du document -->
    {% block document_title %}
    <h3 class="document-title">{% block title %}Document{% endblock %}</h3>
    {% endblock %}
    
    <!-- Contenu principal -->
    <div class="print-content">
        {% block content %}{% endblock %}
    </div>
    
    <!-- Section signature -->
    {% block signature_section %}
    <div class="signature-section no-print-break">
        <div class="row">
            <div class="col-md-6">
                {% if company and company.signature_path %}
                <div class="signature-box">
                    <p class="mb-2"><strong>Signature autorisée:</strong></p>
                    <img src="{{ url_for('static', filename='uploads/' + company.signature_path) }}" 
                         alt="Signature" style="max-height: 60px;">
                </div>
                {% else %}
                <div class="signature-box">
                    <p class="mb-2"><strong>Signature:</strong></p>
                    <div style="height: 60px;"></div>
                </div>
                {% endif %}
            </div>
            <div class="col-md-6">
                {% if company and company.cachet_path %}
                <div class="signature-box">
                    <p class="mb-2"><strong>Cachet de l'entreprise:</strong></p>
                    <img src="{{ url_for('static', filename='uploads/' + company.cachet_path) }}" 
                         alt="Cachet" style="max-height: 60px;">
                </div>
                {% else %}
                <div class="signature-box">
                    <p class="mb-2"><strong>Cachet:</strong></p>
                    <div style="height: 60px;"></div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endblock %}
    
    <!-- Pied de page -->
    <div class="print-footer">
        {% if company and company.pied_de_page %}
        <div class="text-center mb-2">
            {{ company.pied_de_page|nl2br|safe }}
        </div>
        {% endif %}
        
        {% if company %}
        <div class="row small">
            {% if company.numero_registre_commerce %}
            <div class="col-auto">RC: {{ company.numero_registre_commerce }}</div>
            {% endif %}
            {% if company.numero_ice %}
            <div class="col-auto">ICE: {{ company.numero_ice }}</div>
            {% endif %}
            {% if company.numero_if %}
            <div class="col-auto">IF: {{ company.numero_if }}</div>
            {% endif %}
            {% if company.numero_patente %}
            <div class="col-auto">Patente: {{ company.numero_patente }}</div>
            {% endif %}
        </div>
        
        {% if company.mentions_legales %}
        <div class="mt-2 small text-muted">
            {{ company.mentions_legales|nl2br|safe }}
        </div>
        {% endif %}
        {% endif %}
        
        <div class="text-center mt-2 small">
            <em>Document généré automatiquement par le système de Gestion des Pointages</em>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/fr.min.js"></script>
    
    <script>
        // Configuration de moment.js en français
        moment.locale('fr');
        
        // Auto-impression si paramètre dans l'URL
        if (window.location.search.includes('auto_print=1')) {
            window.onload = function() {
                setTimeout(() => {
                    window.print();
                }, 1000);
            };
        }
        
        // Gestion des raccourcis clavier
        document.addEventListener('keydown', function(e) {
            // Ctrl+P pour imprimer
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            // Escape pour fermer
            if (e.key === 'Escape') {
                window.close();
            }
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
