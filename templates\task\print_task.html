<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Imprimer - {{ task.title }}</title>
    <style>
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; padding: 20px; }
        }
        
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .task-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .detail-row {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
        }
        
        .detail-label {
            font-weight: bold;
            width: 150px;
            color: #495057;
        }
        
        .detail-value {
            flex: 1;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-pending { background: #ffc107; color: #000; }
        .status-in_progress { background: #17a2b8; color: #fff; }
        .status-completed { background: #28a745; color: #fff; }
        .status-cancelled { background: #dc3545; color: #fff; }
        
        .priority-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .priority-0 { background: #6c757d; color: #fff; }
        .priority-1 { background: #28a745; color: #fff; }
        .priority-2 { background: #ffc107; color: #000; }
        .priority-3 { background: #dc3545; color: #fff; }
        
        .print-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        
        .print-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="no-print">
        <button class="print-button" onclick="window.print()">🖨️ Imprimer</button>
        <button class="print-button" onclick="window.close()" style="background: #6c757d;">❌ Fermer</button>
    </div>

    <div class="header">
        <h1>Détails de la Tâche</h1>
        <p>Gestion des Pointages - Système de Gestion des Tâches</p>
    </div>

    <div class="task-details">
        <div class="detail-row">
            <div class="detail-label">ID de la tâche:</div>
            <div class="detail-value">#{{ task.id }}</div>
        </div>

        <div class="detail-row">
            <div class="detail-label">Titre:</div>
            <div class="detail-value"><strong>{{ task.title }}</strong></div>
        </div>

        <div class="detail-row">
            <div class="detail-label">Description:</div>
            <div class="detail-value">{{ task.description or 'Aucune description' }}</div>
        </div>

        <div class="detail-row">
            <div class="detail-label">Statut:</div>
            <div class="detail-value">
                {% if task.status == 'pending' %}
                <span class="status-badge status-pending">En attente</span>
                {% elif task.status == 'in_progress' %}
                <span class="status-badge status-in_progress">En cours</span>
                {% elif task.status == 'completed' %}
                <span class="status-badge status-completed">Terminé</span>
                {% else %}
                <span class="status-badge status-cancelled">Annulé</span>
                {% endif %}
            </div>
        </div>

        <div class="detail-row">
            <div class="detail-label">Priorité:</div>
            <div class="detail-value">
                {% if task.priority == 0 %}
                <span class="priority-badge priority-0">Faible</span>
                {% elif task.priority == 1 %}
                <span class="priority-badge priority-1">Normale</span>
                {% elif task.priority == 2 %}
                <span class="priority-badge priority-2">Élevée</span>
                {% else %}
                <span class="priority-badge priority-3">Critique</span>
                {% endif %}
            </div>
        </div>

        <div class="detail-row">
            <div class="detail-label">Date d'échéance:</div>
            <div class="detail-value">{{ task.due_date.strftime('%d/%m/%Y') if task.due_date else 'Non définie' }}</div>
        </div>

        <div class="detail-row">
            <div class="detail-label">Catégorie:</div>
            <div class="detail-value">{{ task.category or 'Aucune catégorie' }}</div>
        </div>

        <div class="detail-row">
            <div class="detail-label">Tags:</div>
            <div class="detail-value">{{ task.tags or 'Aucun tag' }}</div>
        </div>

        <div class="detail-row">
            <div class="detail-label">Assigné à:</div>
            <div class="detail-value">
                {% if task.assigned_user %}
                {{ task.assigned_user.username }}
                {% else %}
                Non assigné
                {% endif %}
            </div>
        </div>

        <div class="detail-row">
            <div class="detail-label">Fichier joint:</div>
            <div class="detail-value">
                {% if task.attachment_path %}
                Oui - {{ task.attachment_path.split('/')[-1] }}
                {% else %}
                Aucun fichier joint
                {% endif %}
            </div>
        </div>

        <div class="detail-row">
            <div class="detail-label">Date de création:</div>
            <div class="detail-value">{{ task.created_at.strftime('%d/%m/%Y à %H:%M') if task.created_at else 'Non définie' }}</div>
        </div>

        <div class="detail-row">
            <div class="detail-label">Dernière modification:</div>
            <div class="detail-value">{{ task.updated_at.strftime('%d/%m/%Y à %H:%M') if task.updated_at else 'Non définie' }}</div>
        </div>
    </div>

    <div style="text-align: center; margin-top: 40px; color: #6c757d; font-size: 12px;">
        <p>Document généré le {{ moment().format('DD/MM/YYYY à HH:mm') }}</p>
        <p>Gestion des Pointages - Système de Gestion des Tâches</p>
    </div>

    <script>
        // Auto-print when opened in new window
        if (window.location.search.includes('autoprint=true')) {
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            };
        }
    </script>
</body>
</html>
