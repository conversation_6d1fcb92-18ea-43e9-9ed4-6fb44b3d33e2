{% extends "base.html" %}

{% block title %}Gestion des tâches - Gestion des Pointages{% endblock %}

{% block styles %}
<style>
    .task-card {
        transition: transform 0.2s, box-shadow 0.2s;
        margin-bottom: 20px;
    }
    .task-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .priority-badge {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .task-status {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
    }
    .status-pending { background-color: #ffc107; }
    .status-in-progress { background-color: #17a2b8; }
    .status-completed { background-color: #28a745; }
    .status-cancelled { background-color: #6c757d; }
    .status-delayed { background-color: #dc3545; }
    
    .priority-0 { border-left: 5px solid #6c757d; } /* Basse */
    .priority-1 { border-left: 5px solid #17a2b8; } /* Normale */
    .priority-2 { border-left: 5px solid #ffc107; } /* Haute */
    .priority-3 { border-left: 5px solid #dc3545; } /* Urgente */
    
    .task-tag {
        display: inline-block;
        padding: 2px 8px;
        margin: 2px;
        border-radius: 12px;
        background-color: #e9ecef;
        font-size: 0.8rem;
    }
    
    .task-due-date {
        font-size: 0.9rem;
    }
    
    .task-due-date.overdue {
        color: #dc3545;
        font-weight: bold;
    }
    
    .filters-card {
        margin-bottom: 20px;
    }
    
    .view-toggle-btn {
        margin-right: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3"><i class="fas fa-tasks me-2"></i>Gestion des tâches</h1>
        <p class="text-muted">Gérez les tâches et assignez-les aux employés.</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('task.add_task') }}" class="btn btn-primary me-2">
            <i class="fas fa-plus me-1"></i> Ajouter une tâche
        </a>
        <a href="{{ url_for('task.assign_task') }}" class="btn btn-success">
            <i class="fas fa-user-check me-1"></i> Assigner une tâche
        </a>
    </div>
</div>

<!-- Filtres -->
<div class="card filters-card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filtres</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3 mb-3">
                <label for="filter-status" class="form-label">Statut</label>
                <select id="filter-status" class="form-select">
                    <option value="">Tous</option>
                    <option value="pending">En attente</option>
                    <option value="in_progress">En cours</option>
                    <option value="completed">Terminée</option>
                    <option value="cancelled">Annulée</option>
                    <option value="delayed">Retardée</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label for="filter-priority" class="form-label">Priorité</label>
                <select id="filter-priority" class="form-select">
                    <option value="">Toutes</option>
                    <option value="0">Basse</option>
                    <option value="1">Normale</option>
                    <option value="2">Haute</option>
                    <option value="3">Urgente</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label for="filter-category" class="form-label">Catégorie</label>
                <select id="filter-category" class="form-select">
                    <option value="">Toutes</option>
                    {% for category in categories %}
                    <option value="{{ category }}">{{ category }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label for="filter-search" class="form-label">Recherche</label>
                <input type="text" id="filter-search" class="form-control" placeholder="Rechercher...">
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 text-end">
                <button id="reset-filters" class="btn btn-secondary">Réinitialiser</button>
            </div>
        </div>
    </div>
</div>

<!-- Boutons de basculement de vue -->
<div class="mb-3">
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-primary view-toggle-btn active" data-view="cards">
            <i class="fas fa-th-large me-1"></i> Cartes
        </button>
        <button type="button" class="btn btn-outline-primary view-toggle-btn" data-view="table">
            <i class="fas fa-table me-1"></i> Tableau
        </button>
    </div>
</div>

<!-- Vue en cartes -->
<div id="cards-view" class="row">
    {% for task in tasks %}
    <div class="col-md-4 task-item" 
         data-status="{{ task.status }}" 
         data-priority="{{ task.priority }}" 
         data-category="{{ task.category }}"
         data-title="{{ task.title }}"
         data-description="{{ task.description }}">
        <div class="card task-card priority-{{ task.priority }}" style="border-color: {{ task.color }};">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{{ task.title }}</h5>
                <span class="badge {% if task.priority == 0 %}bg-secondary{% elif task.priority == 1 %}bg-info{% elif task.priority == 2 %}bg-warning{% elif task.priority == 3 %}bg-danger{% endif %} priority-badge">
                    {{ task.priority_display }}
                </span>
            </div>
            <div class="card-body">
                <p class="card-text">{{ task.description|truncate(100) }}</p>
                
                <div class="mb-2">
                    <span class="task-status status-{{ task.status }}"></span>
                    <span>{{ task.status_display }}</span>
                </div>
                
                {% if task.due_date %}
                <div class="mb-2 task-due-date {% if task.due_date < today %}overdue{% endif %}">
                    <i class="far fa-calendar-alt me-1"></i> Échéance: {{ task.due_date.strftime('%d/%m/%Y') }}
                    {% if task.due_date < today %}
                    <span class="badge bg-danger ms-1">En retard</span>
                    {% endif %}
                </div>
                {% endif %}
                
                {% if task.category %}
                <div class="mb-2">
                    <i class="fas fa-folder me-1"></i> {{ task.category }}
                </div>
                {% endif %}
                
                {% if task.tags %}
                <div class="mb-2">
                    {% for tag in task.tag_list %}
                    <span class="task-tag">{{ tag }}</span>
                    {% endfor %}
                </div>
                {% endif %}
                
                <div class="mt-3 d-flex justify-content-between">
                    <div>
                        <small class="text-muted">Créée le {{ task.created_at.strftime('%d/%m/%Y') }}</small>
                    </div>
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('task.edit_task', id=task.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </a>
                        <a href="{{ url_for('task.delete_task', id=task.id) }}" class="btn btn-sm btn-danger delete-confirm" data-bs-toggle="tooltip" title="Supprimer" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette tâche?')">
                            <i class="fas fa-trash"></i>
                        </a>
                        <button type="button" class="btn btn-sm btn-info print-item" data-bs-toggle="tooltip" title="Imprimer" onclick="printTaskInfo({{ task.id }})">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Vue en tableau -->
<div id="table-view" class="row" style="display: none;">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Titre</th>
                                <th>Statut</th>
                                <th>Priorité</th>
                                <th>Catégorie</th>
                                <th>Échéance</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in tasks %}
                            <tr class="task-item" 
                                data-status="{{ task.status }}" 
                                data-priority="{{ task.priority }}" 
                                data-category="{{ task.category }}"
                                data-title="{{ task.title }}"
                                data-description="{{ task.description }}">
                                <td>{{ task.title }}</td>
                                <td>
                                    <span class="task-status status-{{ task.status }}"></span>
                                    {{ task.status_display }}
                                </td>
                                <td>
                                    <span class="badge {% if task.priority == 0 %}bg-secondary{% elif task.priority == 1 %}bg-info{% elif task.priority == 2 %}bg-warning{% elif task.priority == 3 %}bg-danger{% endif %}">
                                        {{ task.priority_display }}
                                    </span>
                                </td>
                                <td>{{ task.category or '-' }}</td>
                                <td>
                                    {% if task.due_date %}
                                    <span class="{% if task.due_date < today %}text-danger fw-bold{% endif %}">
                                        {{ task.due_date.strftime('%d/%m/%Y') }}
                                    </span>
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('task.edit_task', id=task.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('task.delete_task', id=task.id) }}" class="btn btn-sm btn-danger delete-confirm" data-bs-toggle="tooltip" title="Supprimer" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette tâche?')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-info print-item" data-bs-toggle="tooltip" title="Imprimer" onclick="printTaskInfo({{ task.id }})">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Liens vers les autres pages de tâches -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-calendar-day me-2"></i>Tâches quotidiennes</h5>
                <a href="{{ url_for('task.daily_tasks') }}" class="btn btn-sm btn-primary">Voir tout</a>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> Consultez les tâches quotidiennes pour voir les tâches assignées aux employés et suivre leur progression.
                </div>
                <div class="text-center">
                    <a href="{{ url_for('task.daily_tasks') }}" class="btn btn-primary me-2">
                        <i class="fas fa-calendar-day me-1"></i> Voir les tâches quotidiennes
                    </a>
                    <a href="{{ url_for('task.task_checkboxes') }}" class="btn btn-success">
                        <i class="fas fa-tasks me-1"></i> Liste des 50 tâches
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filtrage des tâches
        function filterTasks() {
            const statusFilter = document.getElementById('filter-status').value;
            const priorityFilter = document.getElementById('filter-priority').value;
            const categoryFilter = document.getElementById('filter-category').value;
            const searchFilter = document.getElementById('filter-search').value.toLowerCase();
            
            document.querySelectorAll('.task-item').forEach(task => {
                const status = task.dataset.status;
                const priority = task.dataset.priority;
                const category = task.dataset.category;
                const title = task.dataset.title.toLowerCase();
                const description = task.dataset.description.toLowerCase();
                
                const statusMatch = !statusFilter || status === statusFilter;
                const priorityMatch = !priorityFilter || priority === priorityFilter;
                const categoryMatch = !categoryFilter || category === categoryFilter;
                const searchMatch = !searchFilter || 
                                   title.includes(searchFilter) || 
                                   description.includes(searchFilter);
                
                if (statusMatch && priorityMatch && categoryMatch && searchMatch) {
                    task.style.display = '';
                } else {
                    task.style.display = 'none';
                }
            });
        }
        
        // Écouteurs d'événements pour les filtres
        document.getElementById('filter-status').addEventListener('change', filterTasks);
        document.getElementById('filter-priority').addEventListener('change', filterTasks);
        document.getElementById('filter-category').addEventListener('change', filterTasks);
        document.getElementById('filter-search').addEventListener('input', filterTasks);
        
        // Réinitialiser les filtres
        document.getElementById('reset-filters').addEventListener('click', function() {
            document.getElementById('filter-status').value = '';
            document.getElementById('filter-priority').value = '';
            document.getElementById('filter-category').value = '';
            document.getElementById('filter-search').value = '';
            filterTasks();
        });
        
        // Basculement entre les vues
        document.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.view-toggle-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const view = this.dataset.view;
                if (view === 'cards') {
                    document.getElementById('cards-view').style.display = '';
                    document.getElementById('table-view').style.display = 'none';
                } else {
                    document.getElementById('cards-view').style.display = 'none';
                    document.getElementById('table-view').style.display = '';
                }
            });
        });
    });
    
    // Fonction pour imprimer les informations de la tâche
    function printTaskInfo(taskId) {
        // Récupérer les données de la tâche
        const taskElement = document.querySelector(`.task-item[data-task-id="${taskId}"]`);
        if (!taskElement) return;
        
        // Créer une fenêtre d'impression
        let printWindow = window.open('', '_blank');
        
        // Contenu HTML à imprimer
        let content = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Fiche Tâche</title>
                <style>
                    body { font-family: Arial, sans-serif; }
                    .header { text-align: center; margin-bottom: 20px; }
                    .info-table { width: 100%; border-collapse: collapse; }
                    .info-table th, .info-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                    .info-table th { background-color: #f2f2f2; width: 30%; }
                    .footer { margin-top: 30px; text-align: center; font-size: 12px; }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Fiche Tâche</h1>
                    <p>Date d'impression: ${new Date().toLocaleDateString()}</p>
                </div>
                
                <table class="info-table">
                    <tr>
                        <th>ID</th>
                        <td>${taskId}</td>
                    </tr>
                    <tr>
                        <th>Titre</th>
                        <td>${taskElement.dataset.title}</td>
                    </tr>
                    <tr>
                        <th>Description</th>
                        <td>${taskElement.dataset.description}</td>
                    </tr>
                    <tr>
                        <th>Statut</th>
                        <td>${taskElement.querySelector('.status-display')?.textContent || '-'}</td>
                    </tr>
                    <tr>
                        <th>Priorité</th>
                        <td>${taskElement.querySelector('.priority-display')?.textContent || '-'}</td>
                    </tr>
                </table>
                
                <div class="footer">
                    <p>Gestion des Pointages - Document généré automatiquement</p>
                </div>
            </body>
            </html>
        `;
        
        // Écrire le contenu dans la fenêtre d'impression
        printWindow.document.open();
        printWindow.document.write(content);
        printWindow.document.close();
        
        // Imprimer après le chargement du contenu
        printWindow.onload = function() {
            printWindow.print();
        };
    }
</script>
{% endblock %}
