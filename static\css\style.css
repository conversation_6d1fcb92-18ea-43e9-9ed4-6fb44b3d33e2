/* Styles personnalisés pour Gestion des Pointages */

:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --success-color: #2ecc71;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

/* Navbar */
.navbar {
    background-color: var(--dark-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    color: white !important;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
    transition: color 0.3s;
}

.navbar-nav .nav-link:hover {
    color: white !important;
}

.navbar-nav .active > .nav-link {
    color: white !important;
    font-weight: 600;
}

/* Cards */
.card {
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: none;
}

.card-header {
    background-color: white;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* Buttons */
.btn {
    border-radius: 4px;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.3s;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-success {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success:hover {
    background-color: #27ae60;
    border-color: #27ae60;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

/* Forms */
.form-control {
    border-radius: 4px;
    padding: 10px 12px;
    border: 1px solid #ddd;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Tables */
.table {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background-color: var(--dark-color);
    color: white;
    border-bottom: none;
    font-weight: 500;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Dashboard cards */
.dashboard-card {
    border-left: 4px solid var(--primary-color);
    transition: transform 0.3s;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.dashboard-card.card-employees {
    border-left-color: var(--primary-color);
}

.dashboard-card.card-present {
    border-left-color: var(--success-color);
}

.dashboard-card.card-absent {
    border-left-color: var(--danger-color);
}

.dashboard-card.card-tasks {
    border-left-color: var(--warning-color);
}

.dashboard-card.card-income {
    border-left-color: var(--success-color);
}

.dashboard-card.card-expenses {
    border-left-color: var(--danger-color);
}

.dashboard-card.card-balance {
    border-left-color: var(--info-color);
}

.dashboard-card .card-icon {
    font-size: 2rem;
    opacity: 0.8;
}

/* Attendance calendar */
.calendar-day {
    height: 120px;
    border: 1px solid #ddd;
    padding: 5px;
    background-color: white;
}

.calendar-day.today {
    background-color: #f8f9fa;
    border: 2px solid var(--primary-color);
}

.calendar-day .day-number {
    font-weight: bold;
    margin-bottom: 5px;
}

.calendar-day .attendance-present {
    background-color: rgba(46, 204, 113, 0.2);
    border-left: 3px solid var(--success-color);
    padding: 2px 5px;
    margin-bottom: 2px;
    font-size: 0.8rem;
}

.calendar-day .attendance-absent {
    background-color: rgba(231, 76, 60, 0.2);
    border-left: 3px solid var(--danger-color);
    padding: 2px 5px;
    margin-bottom: 2px;
    font-size: 0.8rem;
}

.calendar-day .task-complete {
    background-color: rgba(46, 204, 113, 0.2);
    border-left: 3px solid var(--success-color);
    padding: 2px 5px;
    margin-bottom: 2px;
    font-size: 0.8rem;
}

.calendar-day .task-incomplete {
    background-color: rgba(243, 156, 18, 0.2);
    border-left: 3px solid var(--warning-color);
    padding: 2px 5px;
    margin-bottom: 2px;
    font-size: 0.8rem;
}

/* Login page */
.login-container {
    max-width: 400px;
    margin: 100px auto;
}

.login-logo {
    text-align: center;
    margin-bottom: 30px;
}

.login-card {
    border-radius: 10px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: white;
    padding: 20px 0;
    margin-top: 50px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-card {
        margin-bottom: 15px;
    }

    .calendar-day {
        height: auto;
        min-height: 100px;
    }
}

/* ===== STYLES KANBAN BOARD ===== */

/* Variables pour le Kanban */
:root {
    --kanban-bg: #f8f9fa;
    --kanban-column-bg: #ffffff;
    --kanban-shadow: 0 2px 4px rgba(0,0,0,0.1);
    --kanban-hover-shadow: 0 4px 8px rgba(0,0,0,0.15);
    --kanban-border-radius: 8px;
}

/* Conteneur principal du Kanban */
.kanban-board {
    background-color: var(--kanban-bg);
    padding: 20px;
    border-radius: var(--kanban-border-radius);
    margin-bottom: 20px;
    min-height: 500px;
}

/* Colonnes du Kanban */
.kanban-column {
    background-color: var(--kanban-column-bg);
    border-radius: var(--kanban-border-radius);
    box-shadow: var(--kanban-shadow);
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.kanban-column:hover {
    box-shadow: var(--kanban-hover-shadow);
}

/* En-têtes des colonnes */
.kanban-header {
    padding: 15px;
    border-radius: var(--kanban-border-radius) var(--kanban-border-radius) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
}

.kanban-header h5 {
    margin: 0;
    font-size: 1rem;
}

/* Corps des colonnes */
.kanban-body {
    padding: 15px;
    min-height: 400px;
    max-height: 600px;
    overflow-y: auto;
    background-color: #fafbfc;
}

/* Cartes de tâches */
.task-card {
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 12px;
}

.task-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--kanban-hover-shadow);
}

.task-card .card {
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    border-radius: 6px;
}

.task-card:hover .card {
    border-color: var(--primary-color);
}

.task-card .card-body {
    padding: 12px;
}

.task-card .card-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.3;
}

.task-card .card-text {
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 8px;
}

/* Badges de priorité */
.priority-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
}

/* Compteurs de tâches */
.task-count {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 600;
    min-width: 24px;
    text-align: center;
}

/* Boutons d'action dans les cartes */
.task-card .btn-group-sm .btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
    border-radius: 4px;
}

/* Animations pour le drag & drop */
.task-card.dragging {
    opacity: 0.5;
    transform: rotate(2deg) scale(0.95);
    z-index: 1000;
}

.kanban-body.drag-over {
    background-color: #e3f2fd;
    border: 2px dashed #2196f3;
    border-radius: 6px;
}

/* Couleurs des en-têtes par statut */
.kanban-header.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
}

.kanban-header.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.kanban-header.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.kanban-header.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
}

/* Responsive pour le Kanban */
@media (max-width: 768px) {
    .kanban-board {
        padding: 10px;
    }

    .kanban-board .row {
        flex-direction: column;
    }

    .kanban-column {
        margin-bottom: 15px;
    }

    .kanban-body {
        min-height: 200px;
        max-height: 300px;
        padding: 10px;
    }

    .kanban-header {
        padding: 10px;
    }

    .task-card .card-body {
        padding: 8px;
    }
}

/* Boutons de vue */
.btn-group .btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}

/* Filtres actifs */
.filter-active {
    background-color: var(--primary-color) !important;
    color: white !important;
    border-color: var(--primary-color) !important;
}

/* Améliorations pour la vue tableau */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 12px 8px;
}

.table td {
    vertical-align: middle;
    padding: 12px 8px;
    font-size: 0.9rem;
}

/* Indicateurs de couleur dans le tableau */
.table .color-indicator {
    width: 4px;
    height: 30px;
    border-radius: 2px;
    display: inline-block;
    margin-right: 8px;
}

/* Badges personnalisés */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.35rem 0.6rem;
}

/* Barre de recherche améliorée */
#search-tasks {
    border-radius: 25px;
    padding-left: 40px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

#search-tasks:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Animations d'entrée */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.task-card {
    animation: fadeInUp 0.3s ease-out;
}

/* Scrollbar personnalisée pour les colonnes Kanban */
.kanban-body::-webkit-scrollbar {
    width: 6px;
}

.kanban-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.kanban-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.kanban-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
