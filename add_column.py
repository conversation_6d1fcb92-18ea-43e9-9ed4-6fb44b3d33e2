#!/usr/bin/env python3
"""
Script pour ajouter la colonne assigned_to à la table tasks
"""

import sqlite3
import os

def add_assigned_to_column():
    """Ajouter la colonne assigned_to à la table tasks"""
    
    # Chemin vers la base de données
    db_path = 'instance/app.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Base de données non trouvée: {db_path}")
        return False
    
    try:
        # Connexion à la base de données
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Vérifier si la colonne existe déjà
        cursor.execute("PRAGMA table_info(tasks)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'assigned_to' in columns:
            print("ℹ️ Colonne 'assigned_to' existe déjà dans la table 'tasks'")
            conn.close()
            return True
        
        # Ajouter la colonne assigned_to
        cursor.execute("ALTER TABLE tasks ADD COLUMN assigned_to INTEGER")
        conn.commit()
        
        print("✅ Colonne 'assigned_to' ajoutée avec succès à la table 'tasks'")
        
        # Vérifier que la colonne a été ajoutée
        cursor.execute("PRAGMA table_info(tasks)")
        columns = [row[1] for row in cursor.fetchall()]
        
        if 'assigned_to' in columns:
            print("✅ Vérification: Colonne 'assigned_to' présente")
        else:
            print("❌ Erreur: Colonne 'assigned_to' non trouvée après ajout")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de l'ajout de la colonne: {e}")
        if 'conn' in locals():
            conn.close()
        return False

if __name__ == "__main__":
    print("🔧 Ajout de la colonne 'assigned_to' à la table 'tasks'...")
    success = add_assigned_to_column()
    
    if success:
        print("🎉 Migration terminée avec succès!")
    else:
        print("💥 Migration échouée!")
