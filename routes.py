from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, abort, send_from_directory
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.urls import url_parse
from werkzeug.utils import secure_filename
from models import User, Employee, Task, EmployeeTask, Attendance, Transaction
from forms import (LoginForm, RegistrationForm, UserEditForm, EmployeeForm,
                  TaskForm, EmployeeTaskForm, AttendanceForm, TransactionForm)
from extensions import db
import os
from datetime import datetime, date, timedelta
from sqlalchemy import func, desc, or_
import calendar
import locale
import json

# Blueprints
auth_bp = Blueprint('auth', __name__)
user_bp = Blueprint('user', __name__)
employee_bp = Blueprint('employee', __name__)
task_bp = Blueprint('task', __name__)
attendance_bp = Blueprint('attendance', __name__)
finance_bp = Blueprint('finance', __name__)
main_bp = Blueprint('main', __name__)

# Routes d'authentification
@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user is None or not user.check_password(form.password.data):
            flash('Nom d\'utilisateur ou mot de passe incorrect', 'danger')
            return redirect(url_for('auth.login'))

        login_user(user, remember=form.remember_me.data)
        user.last_login = datetime.utcnow()
        db.session.commit()

        next_page = request.args.get('next')
        if not next_page or url_parse(next_page).netloc != '':
            next_page = url_for('main.dashboard')
        return redirect(next_page)

    return render_template('auth/login.html', title='Connexion', form=form)

@auth_bp.route('/logout')
def logout():
    logout_user()
    return redirect(url_for('auth.login'))

# Routes principales
@main_bp.route('/')
@main_bp.route('/dashboard')
@login_required
def dashboard():
    today = date.today()

    # Statistiques pour le tableau de bord
    employee_count = Employee.query.filter_by(active=True).count()
    present_today = Attendance.query.filter_by(date=today, present=True).count()
    absent_today = Attendance.query.filter_by(date=today, present=False).count()

    # Transactions récentes
    recent_transactions = Transaction.query.order_by(desc(Transaction.date)).limit(5).all()

    # Calcul des totaux financiers
    income = db.session.query(func.sum(Transaction.amount)).filter(Transaction.transaction_type == 'encaissement').scalar() or 0
    expenses = db.session.query(func.sum(Transaction.amount)).filter(Transaction.transaction_type == 'decaissement').scalar() or 0
    balance = income - expenses

    # Tâches à faire aujourd'hui
    today_tasks = EmployeeTask.query.filter_by(date=today).all()
    completed_tasks = sum(1 for task in today_tasks if task.completed)
    total_tasks = len(today_tasks)

    return render_template('main/dashboard.html',
                          title='Tableau de bord',
                          employee_count=employee_count,
                          present_today=present_today,
                          absent_today=absent_today,
                          recent_transactions=recent_transactions,
                          income=income,
                          expenses=expenses,
                          balance=balance,
                          completed_tasks=completed_tasks,
                          total_tasks=total_tasks)

# Routes de gestion des utilisateurs
@user_bp.route('/users')
@login_required
def users():
    if not current_user.can_manage_users and not current_user.is_admin:
        abort(403)

    users = User.query.all()
    return render_template('user/users.html', title='Gestion des utilisateurs', users=users)

@user_bp.route('/user/add', methods=['GET', 'POST'])
@login_required
def add_user():
    if not current_user.can_manage_users and not current_user.is_admin:
        abort(403)

    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            is_admin=form.is_admin.data,
            can_manage_users=form.can_manage_users.data,
            can_manage_employees=form.can_manage_employees.data,
            can_manage_tasks=form.can_manage_tasks.data,
            can_manage_finances=form.can_manage_finances.data,
            can_access_registration_files=form.can_access_registration_files.data,
            can_access_technical_files=form.can_access_technical_files.data,
            can_access_reimbursement_files=form.can_access_reimbursement_files.data,
            can_access_organization_files=form.can_access_organization_files.data,
            can_access_trainer_files=form.can_access_trainer_files.data,
            can_access_trainer_schedule=form.can_access_trainer_schedule.data
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()
        flash('Utilisateur ajouté avec succès!', 'success')
        return redirect(url_for('user.users'))

    return render_template('user/add_user.html', title='Ajouter un utilisateur', form=form)

@user_bp.route('/user/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_user(id):
    if not current_user.can_manage_users and not current_user.is_admin:
        abort(403)

    user = User.query.get_or_404(id)
    form = UserEditForm()

    if form.validate_on_submit():
        user.username = form.username.data
        user.email = form.email.data
        user.is_admin = form.is_admin.data
        user.can_manage_users = form.can_manage_users.data
        user.can_manage_employees = form.can_manage_employees.data
        user.can_manage_tasks = form.can_manage_tasks.data
        user.can_manage_finances = form.can_manage_finances.data
        user.can_access_registration_files = form.can_access_registration_files.data
        user.can_access_technical_files = form.can_access_technical_files.data
        user.can_access_reimbursement_files = form.can_access_reimbursement_files.data
        user.can_access_organization_files = form.can_access_organization_files.data
        user.can_access_trainer_files = form.can_access_trainer_files.data
        user.can_access_trainer_schedule = form.can_access_trainer_schedule.data

        db.session.commit()
        flash('Utilisateur mis à jour avec succès!', 'success')
        return redirect(url_for('user.users'))

    elif request.method == 'GET':
        form.username.data = user.username
        form.email.data = user.email
        form.is_admin.data = user.is_admin
        form.can_manage_users.data = user.can_manage_users
        form.can_manage_employees.data = user.can_manage_employees
        form.can_manage_tasks.data = user.can_manage_tasks
        form.can_manage_finances.data = user.can_manage_finances
        form.can_access_registration_files.data = user.can_access_registration_files
        form.can_access_technical_files.data = user.can_access_technical_files
        form.can_access_reimbursement_files.data = user.can_access_reimbursement_files
        form.can_access_organization_files.data = user.can_access_organization_files
        form.can_access_trainer_files.data = user.can_access_trainer_files
        form.can_access_trainer_schedule.data = user.can_access_trainer_schedule

    return render_template('user/edit_user.html', title='Modifier un utilisateur', form=form, user=user)

@user_bp.route('/user/delete/<int:id>', methods=['POST'])
@login_required
def delete_user(id):
    if not current_user.can_manage_users and not current_user.is_admin:
        abort(403)

    user = User.query.get_or_404(id)
    if user.id == current_user.id:
        flash('Vous ne pouvez pas supprimer votre propre compte!', 'danger')
    else:
        db.session.delete(user)
        db.session.commit()
        flash('Utilisateur supprimé avec succès!', 'success')

    return redirect(url_for('user.users'))

# Routes de gestion des employés
@employee_bp.route('/employees')
@login_required
def employees():
    if not current_user.can_manage_employees and not current_user.is_admin:
        abort(403)

    employees = Employee.query.all()
    return render_template('employee/employees.html', title='Gestion des employés', employees=employees)

@employee_bp.route('/employee/add', methods=['GET', 'POST'])
@login_required
def add_employee():
    if not current_user.can_manage_employees and not current_user.is_admin:
        abort(403)

    form = EmployeeForm()
    if form.validate_on_submit():
        employee = Employee(
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            position=form.position.data,
            cin=form.cin.data,
            cnss=form.cnss.data,
            phone=form.phone.data,
            email=form.email.data,
            daily_rate=form.daily_rate.data,
            hire_date=form.hire_date.data,
            notes=form.notes.data
        )
        db.session.add(employee)
        db.session.commit()
        flash('Employé ajouté avec succès!', 'success')
        return redirect(url_for('employee.employees'))

    return render_template('employee/add_employee.html', title='Ajouter un employé', form=form)

@employee_bp.route('/employee/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_employee(id):
    if not current_user.can_manage_employees and not current_user.is_admin:
        abort(403)

    employee = Employee.query.get_or_404(id)
    form = EmployeeForm()

    if form.validate_on_submit():
        employee.first_name = form.first_name.data
        employee.last_name = form.last_name.data
        employee.position = form.position.data
        employee.cin = form.cin.data
        employee.cnss = form.cnss.data
        employee.phone = form.phone.data
        employee.email = form.email.data
        employee.daily_rate = form.daily_rate.data
        employee.hire_date = form.hire_date.data
        employee.notes = form.notes.data

        db.session.commit()
        flash('Employé mis à jour avec succès!', 'success')
        return redirect(url_for('employee.employees'))

    elif request.method == 'GET':
        form.first_name.data = employee.first_name
        form.last_name.data = employee.last_name
        form.position.data = employee.position
        form.cin.data = employee.cin
        form.cnss.data = employee.cnss
        form.phone.data = employee.phone
        form.email.data = employee.email
        form.daily_rate.data = employee.daily_rate
        form.hire_date.data = employee.hire_date
        form.notes.data = employee.notes

    return render_template('employee/edit_employee.html', title='Modifier un employé', form=form, employee=employee)

@employee_bp.route('/employee/delete/<int:id>', methods=['POST'])
@login_required
def delete_employee(id):
    if not current_user.can_manage_employees and not current_user.is_admin:
        abort(403)

    employee = Employee.query.get_or_404(id)
    db.session.delete(employee)
    db.session.commit()
    flash('Employé supprimé avec succès!', 'success')

    return redirect(url_for('employee.employees'))

# Routes de gestion des tâches
@task_bp.route('/tasks')
@login_required
def tasks():
    if not current_user.can_manage_tasks and not current_user.is_admin:
        abort(403)

    # Récupérer toutes les tâches
    tasks = Task.query.all()

    # Récupérer toutes les catégories uniques pour les filtres
    categories = set()
    for task in tasks:
        if task.category:
            categories.add(task.category)

    # Obtenir la date d'aujourd'hui pour vérifier les tâches en retard
    today = date.today()

    # Organiser les tâches par groupes de dates
    task_groups = {
        'past_dates': [],
        'today': [],
        'this_week': [],
        'next_week': [],
        'later': [],
        'no_date': []
    }

    for task in tasks:
        if task.due_date:
            if task.due_date < today:
                task_groups['past_dates'].append(task)
            elif task.due_date == today:
                task_groups['today'].append(task)
            elif task.due_date <= today + timedelta(days=7):
                task_groups['this_week'].append(task)
            elif task.due_date <= today + timedelta(days=14):
                task_groups['next_week'].append(task)
            else:
                task_groups['later'].append(task)
        else:
            task_groups['no_date'].append(task)

    return render_template('task/tasks.html',
                          title='Gestion des tâches',
                          tasks=tasks,
                          task_groups=task_groups,
                          categories=list(categories))

@task_bp.route('/task/add', methods=['GET', 'POST'])
@login_required
def add_task():
    if not current_user.can_manage_tasks and not current_user.is_admin:
        abort(403)

    # Importer date au début de la fonction
    from datetime import date
    import os

    form = TaskForm()

    # Remplir les choix pour assigned_to
    users = User.query.all()
    form.assigned_to.choices = [(0, 'Non assigné')] + [(user.id, user.username) for user in users]

    # Remplir les choix pour employee_id
    employees = Employee.query.all()
    form.employee_id.choices = [(0, 'Aucun employé')] + [(emp.id, emp.full_name) for emp in employees]

    if form.validate_on_submit():
        task = Task(
            title=form.title.data,
            description=form.description.data,
            priority=form.priority.data,
            status=form.status.data,
            due_date=form.due_date.data,
            category=form.category.data,
            tags=form.tags.data,
            color=form.color.data,
            assigned_to=form.assigned_to.data if form.assigned_to.data > 0 else None,
            attachment_path=None  # Sera mis à jour si un fichier est téléchargé
        )
        db.session.add(task)
        db.session.commit()

        # Traitement de la pièce jointe
        attachment_path = None
        if form.attachment.data:
            # Sauvegarder le fichier
            filename = secure_filename(form.attachment.data.filename)
            # Créer un nom de fichier unique avec l'ID de la tâche
            unique_filename = f"task_{task.id}_{filename}"
            # Chemin complet du fichier
            file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
            # Sauvegarder le fichier
            form.attachment.data.save(file_path)
            # Stocker le chemin relatif
            attachment_path = os.path.join('uploads', unique_filename)
            # Mettre à jour le chemin dans la base de données
            task.attachment_path = attachment_path

        # Si un employé a été sélectionné, assigner la tâche à cet employé
        if form.employee_id.data and form.employee_id.data > 0:
            employee_task = EmployeeTask(
                employee_id=form.employee_id.data,
                task_id=task.id,
                date=date.today(),
                start_date=form.start_date.data,
                end_date=form.end_date.data,
                priority=form.priority.data,
                status=form.status.data,
                progress=0,  # Progression initiale à 0%
                notes="Tâche assignée lors de la création"
            )
            db.session.add(employee_task)
            db.session.commit()
            flash('Tâche ajoutée et assignée à l\'employé avec succès!', 'success')
        else:
            flash('Tâche ajoutée avec succès!', 'success')

        return redirect(url_for('task.tasks'))

    # Récupérer les tâches quotidiennes pour les afficher dans la même page
    selected_date = request.args.get('date', date.today().isoformat())
    try:
        selected_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
    except ValueError:
        selected_date = date.today()

    # Classe pour représenter les tâches quotidiennes
    class DailyTaskCheckbox:
        def __init__(self, id=None, description='', completed=False):
            self.id = id
            self.description = description
            self.completed = completed

    # Récupérer les tâches existantes depuis la base de données
    employee_tasks = EmployeeTask.query.filter_by(date=selected_date).all()

    # Convertir en format adapté pour l'affichage
    tasks = []
    for i, task in enumerate(employee_tasks[:50]):  # Limiter à 50 tâches
        tasks.append(DailyTaskCheckbox(
            id=task.id,
            description=f"{task.task.title} - {task.employee.full_name}",
            completed=task.completed
        ))

    return render_template('task/add_task.html',
                          title='Ajouter une tâche',
                          form=form,
                          tasks=tasks,
                          selected_date=selected_date,
                          date=date)

@task_bp.route('/task/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_task(id):
    if not current_user.can_manage_tasks and not current_user.is_admin:
        abort(403)

    task = Task.query.get_or_404(id)
    form = TaskForm()

    # Remplir les choix pour assigned_to
    users = User.query.all()
    form.assigned_to.choices = [(0, 'Non assigné')] + [(user.id, user.username) for user in users]

    # Remplir les choix pour employee_id
    employees = Employee.query.all()
    form.employee_id.choices = [(0, 'Aucun employé')] + [(emp.id, emp.full_name) for emp in employees]

    if form.validate_on_submit():
        task.title = form.title.data
        task.description = form.description.data
        task.priority = form.priority.data
        task.status = form.status.data
        task.due_date = form.due_date.data
        task.category = form.category.data
        task.tags = form.tags.data
        task.color = form.color.data
        task.assigned_to = form.assigned_to.data if form.assigned_to.data > 0 else None

        # Traitement de la pièce jointe
        if form.attachment.data:
            import os
            from werkzeug.utils import secure_filename

            # Sauvegarder le fichier
            filename = secure_filename(form.attachment.data.filename)
            # Créer un nom de fichier unique avec l'ID de la tâche
            unique_filename = f"task_{task.id}_{filename}"
            try:
                # Chemin complet du fichier
                file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
                # Créer le dossier s'il n'existe pas
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                # Sauvegarder le fichier
                form.attachment.data.save(file_path)
                # Stocker le chemin relatif
                attachment_path = os.path.join('uploads', unique_filename)
                # Mettre à jour le chemin dans la base de données
                task.attachment_path = attachment_path
                print(f"Fichier sauvegardé avec succès: {file_path}")
            except Exception as e:
                print(f"Erreur lors de la sauvegarde du fichier: {e}")
                flash(f"Erreur lors de la sauvegarde du fichier: {e}", "danger")

        db.session.commit()
        flash('Tâche mise à jour avec succès!', 'success')
        return redirect(url_for('task.tasks'))

    elif request.method == 'GET':
        form.title.data = task.title
        form.description.data = task.description
        form.priority.data = task.priority
        form.status.data = task.status
        form.due_date.data = task.due_date
        form.category.data = task.category
        form.tags.data = task.tags
        form.color.data = task.color
        form.assigned_to.data = task.assigned_to if task.assigned_to else 0

    return render_template('task/edit_task.html', title='Modifier une tâche', form=form, task=task)

@task_bp.route('/task/delete/<int:id>', methods=['GET', 'POST'])
@login_required
def delete_task(id):
    if not current_user.can_manage_tasks and not current_user.is_admin:
        abort(403)

    task = Task.query.get_or_404(id)

    # Supprimer toutes les assignations de cette tâche
    EmployeeTask.query.filter_by(task_id=id).delete()

    # Supprimer la tâche elle-même
    db.session.delete(task)
    db.session.commit()
    flash('Tâche supprimée avec succès!', 'success')

    # Rediriger vers la page d'où provient la demande
    referrer = request.referrer
    if referrer and 'daily_tasks' in referrer:
        return redirect(url_for('task.daily_tasks'))
    else:
        return redirect(url_for('task.tasks'))

@task_bp.route('/task/print/<int:id>')
@login_required
def print_task(id):
    if not current_user.can_manage_tasks and not current_user.is_admin:
        abort(403)

    task = Task.query.get_or_404(id)
    return render_template('task/print_task.html', title='Imprimer la tâche', task=task)

@task_bp.route('/task/assign', methods=['GET', 'POST'])
@login_required
def assign_task():
    if not current_user.can_manage_tasks and not current_user.is_admin:
        abort(403)

    form = EmployeeTaskForm()
    form.employee_id.choices = [(e.id, e.full_name) for e in Employee.query.filter_by(active=True).all()]
    form.task_id.choices = [(t.id, t.title) for t in Task.query.all()]

    if form.validate_on_submit():
        employee_task = EmployeeTask(
            employee_id=form.employee_id.data,
            task_id=form.task_id.data,
            date=form.date.data,
            start_date=form.start_date.data,
            end_date=form.end_date.data,
            priority=form.priority.data,
            status=form.status.data,
            progress=form.progress.data,
            notes=form.notes.data
        )
        db.session.add(employee_task)
        db.session.commit()
        flash('Tâche assignée avec succès!', 'success')
        return redirect(url_for('task.daily_tasks'))

    return render_template('task/assign_task.html', title='Assigner une tâche', form=form)

@task_bp.route('/tasks/daily', methods=['GET'])
@login_required
def daily_tasks():
    selected_date = request.args.get('date', date.today().isoformat())
    try:
        selected_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
    except ValueError:
        selected_date = date.today()

    employee_tasks = EmployeeTask.query.filter_by(date=selected_date).all()

    return render_template('task/daily_tasks.html',
                          title='Tâches quotidiennes',
                          employee_tasks=employee_tasks,
                          selected_date=selected_date)

@task_bp.route('/task/complete/<int:id>', methods=['POST'])
@login_required
def complete_task(id):
    employee_task = EmployeeTask.query.get_or_404(id)
    employee_task.completed = True
    employee_task.completion_date = datetime.utcnow()
    db.session.commit()

    return jsonify({'success': True})

# Routes de gestion des présences
@attendance_bp.route('/attendance/calendar')
@login_required
def attendance_calendar():
    from datetime import date
    year = request.args.get('year', date.today().year, type=int)
    month = request.args.get('month', date.today().month, type=int)

    # Obtenir le premier jour du mois et le nombre de jours
    first_day = date(year, month, 1)
    days_in_month = calendar.monthrange(year, month)[1]

    # Créer un calendrier avec les jours du mois
    cal = []
    day = 1
    for week in range(6):
        week_days = []
        for weekday in range(7):
            if (week == 0 and weekday < first_day.weekday()) or day > days_in_month:
                week_days.append(None)
            else:
                current_date = date(year, month, day)
                attendances = Attendance.query.filter_by(date=current_date).all()
                tasks = EmployeeTask.query.filter_by(date=current_date).all()

                week_days.append({
                    'day': day,
                    'date': current_date,
                    'attendances': attendances,
                    'tasks': tasks,
                    'is_today': current_date == date.today()
                })
                day += 1
        cal.append(week_days)
        if day > days_in_month:
            break

    # Obtenir le mois précédent et suivant pour la navigation
    prev_month = month - 1 if month > 1 else 12
    prev_year = year if month > 1 else year - 1
    next_month = month + 1 if month < 12 else 1
    next_year = year if month < 12 else year + 1

    return render_template('attendance/calendar.html',
                          title='Calendrier des présences',
                          calendar=cal,
                          month=month,
                          year=year,
                          month_name=calendar.month_name[month],
                          prev_month=prev_month,
                          prev_year=prev_year,
                          next_month=next_month,
                          next_year=next_year,
                          date=date)

@attendance_bp.route('/attendance/daily', methods=['GET', 'POST'])
@login_required
def daily_attendance():
    from datetime import date
    selected_date = request.args.get('date', date.today().isoformat())
    try:
        selected_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
    except ValueError:
        selected_date = date.today()

    employees = Employee.query.filter_by(active=True).all()
    attendances = {a.employee_id: a for a in Attendance.query.filter_by(date=selected_date).all()}

    if request.method == 'POST':
        for employee in employees:
            present = request.form.get(f'present_{employee.id}') == 'on'
            notes = request.form.get(f'notes_{employee.id}', '')

            attendance = attendances.get(employee.id)
            if attendance:
                attendance.present = present
                attendance.notes = notes
            else:
                attendance = Attendance(
                    employee_id=employee.id,
                    date=selected_date,
                    present=present,
                    notes=notes
                )
                db.session.add(attendance)

        db.session.commit()
        flash('Présences enregistrées avec succès!', 'success')
        return redirect(url_for('attendance.attendance_calendar'))

    return render_template('attendance/daily_attendance.html',
                          title='Présences quotidiennes',
                          employees=employees,
                          attendances=attendances,
                          selected_date=selected_date,
                          date=date)

# Routes de gestion des finances
@finance_bp.route('/finance/income', methods=['GET', 'POST'])
@login_required
def income():
    if not current_user.can_manage_finances and not current_user.is_admin:
        abort(403)

    form = TransactionForm()
    form.transaction_type.data = 'encaissement'

    if form.validate_on_submit():
        transaction = Transaction(
            date=form.date.data,
            description=form.description.data,
            amount=form.amount.data,
            transaction_type='encaissement',
            created_by=current_user.id
        )
        db.session.add(transaction)
        db.session.commit()
        flash('Encaissement enregistré avec succès!', 'success')
        return redirect(url_for('finance.income'))

    # Récupérer les encaissements
    transactions = Transaction.query.filter_by(transaction_type='encaissement').order_by(desc(Transaction.date)).all()

    # Calculer le total
    total = db.session.query(func.sum(Transaction.amount)).filter(Transaction.transaction_type == 'encaissement').scalar() or 0

    return render_template('finance/income.html',
                          title='Encaissements',
                          form=form,
                          transactions=transactions,
                          total=total)

@finance_bp.route('/finance/expenses', methods=['GET', 'POST'])
@login_required
def expenses():
    if not current_user.can_manage_finances and not current_user.is_admin:
        abort(403)

    form = TransactionForm()
    form.transaction_type.data = 'decaissement'

    if form.validate_on_submit():
        transaction = Transaction(
            date=form.date.data,
            description=form.description.data,
            amount=form.amount.data,
            transaction_type='decaissement',
            created_by=current_user.id
        )
        db.session.add(transaction)
        db.session.commit()
        flash('Décaissement enregistré avec succès!', 'success')
        return redirect(url_for('finance.expenses'))

    # Récupérer les décaissements
    transactions = Transaction.query.filter_by(transaction_type='decaissement').order_by(desc(Transaction.date)).all()

    # Calculer le total
    total = db.session.query(func.sum(Transaction.amount)).filter(Transaction.transaction_type == 'decaissement').scalar() or 0

    return render_template('finance/expenses.html',
                          title='Décaissements',
                          form=form,
                          transactions=transactions,
                          total=total)

@finance_bp.route('/finance/reports')
@login_required
def reports():
    if not current_user.can_manage_finances and not current_user.is_admin:
        abort(403)

    # Récupérer les paramètres de filtre
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    transaction_type = request.args.get('type')

    # Construire la requête de base
    query = Transaction.query

    # Appliquer les filtres
    if start_date:
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(Transaction.date >= start_date)
        except ValueError:
            pass

    if end_date:
        try:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(Transaction.date <= end_date)
        except ValueError:
            pass

    if transaction_type in ['encaissement', 'decaissement']:
        query = query.filter(Transaction.transaction_type == transaction_type)

    # Exécuter la requête
    transactions = query.order_by(desc(Transaction.date)).all()

    # Calculer les totaux
    income = sum(t.amount for t in transactions if t.transaction_type == 'encaissement')
    expenses = sum(t.amount for t in transactions if t.transaction_type == 'decaissement')
    balance = income - expenses

    return render_template('finance/reports.html',
                          title='Rapports financiers',
                          transactions=transactions,
                          income=income,
                          expenses=expenses,
                          balance=balance,
                          start_date=start_date,
                          end_date=end_date,
                          transaction_type=transaction_type)

@finance_bp.route('/finance/transaction/delete/<int:id>', methods=['POST'])
@login_required
def delete_transaction(id):
    if not current_user.can_manage_finances and not current_user.is_admin:
        abort(403)

    transaction = Transaction.query.get_or_404(id)
    transaction_type = transaction.transaction_type
    db.session.delete(transaction)
    db.session.commit()
    flash('Transaction supprimée avec succès!', 'success')

    if transaction_type == 'encaissement':
        return redirect(url_for('finance.income'))
    else:
        return redirect(url_for('finance.expenses'))

# Routes pour les 50 cases de tâches quotidiennes
@task_bp.route('/tasks/checkboxes', methods=['GET'])
@login_required
def task_checkboxes():
    from datetime import date
    selected_date = request.args.get('date', date.today().isoformat())
    try:
        selected_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
    except ValueError:
        selected_date = date.today()

    # Récupérer les tâches existantes pour cette date
    class DailyTaskCheckbox:
        def __init__(self, id=None, description='', completed=False):
            self.id = id
            self.description = description
            self.completed = completed

    # Récupérer les tâches existantes depuis la base de données
    # Pour simplifier, nous utiliserons les EmployeeTask existants
    employee_tasks = EmployeeTask.query.filter_by(date=selected_date).all()

    # Convertir en format adapté pour l'affichage
    tasks = []
    for i, task in enumerate(employee_tasks[:50]):  # Limiter à 50 tâches
        tasks.append(DailyTaskCheckbox(
            id=task.id,
            description=f"{task.task.title} - {task.employee.full_name}",
            completed=task.completed
        ))

    return render_template('task/task_checkboxes.html',
                          title='Liste des tâches quotidiennes',
                          tasks=tasks,
                          selected_date=selected_date,
                          date=date)

@task_bp.route('/tasks/checkboxes/save', methods=['POST'])
@login_required
def save_task_checkboxes():
    from datetime import date
    selected_date = request.form.get('date')
    try:
        selected_date = datetime.strptime(selected_date, '%Y-%m-%d').date()
    except ValueError:
        selected_date = date.today()

    # Vérifier si nous avons des données pour ajouter une nouvelle tâche
    employee_id = request.form.get('employee_id')
    task_title = request.form.get('title')
    task_description = request.form.get('description')
    attachment_file = request.files.get('attachment')

    # Si nous avons un titre de tâche, c'est qu'on veut ajouter une nouvelle tâche
    if task_title:
        # Créer la nouvelle tâche
        task = Task(
            title=task_title,
            description=task_description,
            attachment_path=None
        )
        db.session.add(task)
        db.session.commit()

        # Traitement de la pièce jointe
        if attachment_file and attachment_file.filename:
            import os
            from werkzeug.utils import secure_filename

            # Sauvegarder le fichier
            filename = secure_filename(attachment_file.filename)
            # Créer un nom de fichier unique avec l'ID de la tâche
            unique_filename = f"task_{task.id}_{filename}"
            try:
                # Chemin complet du fichier
                file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
                # Créer le dossier s'il n'existe pas
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                # Sauvegarder le fichier
                attachment_file.save(file_path)
                # Stocker le chemin relatif
                attachment_path = os.path.join('uploads', unique_filename)
                # Mettre à jour le chemin dans la base de données
                task.attachment_path = attachment_path
                print(f"Fichier sauvegardé avec succès: {file_path}")
            except Exception as e:
                print(f"Erreur lors de la sauvegarde du fichier: {e}")
                flash(f"Erreur lors de la sauvegarde du fichier: {e}", "danger")
            db.session.commit()

        # Si un employé a été sélectionné, assigner la tâche à cet employé
        if employee_id and int(employee_id) > 0:
            employee_task = EmployeeTask(
                employee_id=int(employee_id),
                task_id=task.id,
                date=date.today(),
                notes="Tâche assignée lors de la création"
            )
            db.session.add(employee_task)
            db.session.commit()
            flash('Tâche ajoutée et assignée à l\'employé avec succès!', 'success')
        else:
            flash('Tâche ajoutée avec succès!', 'success')

    # Traiter les 50 tâches
    for i in range(1, 51):
        task_description = request.form.get(f'task_{i}', '').strip()
        task_completed = request.form.get(f'completed_{i}') is not None

        if task_description:
            # Vérifier si une tâche existe déjà pour cet index
            # Pour simplifier, nous créerons une nouvelle tâche si nécessaire
            task = Task.query.filter_by(title=f"Tâche quotidienne {i}").first()
            if not task:
                task = Task(title=f"Tâche quotidienne {i}", description=task_description)
                db.session.add(task)
                db.session.commit()

            # Créer ou mettre à jour l'assignation de tâche
            employee_task = EmployeeTask.query.filter_by(
                task_id=task.id,
                date=selected_date
            ).first()

            if not employee_task:
                # Assigner à l'administrateur par défaut
                admin = User.query.filter_by(is_admin=True).first()
                if admin:
                    employee = Employee.query.first()  # Prendre le premier employé disponible
                    if employee:
                        employee_task = EmployeeTask(
                            employee_id=employee.id,
                            task_id=task.id,
                            date=selected_date,
                            notes=task_description,
                            completed=task_completed
                        )
                        db.session.add(employee_task)
            else:
                employee_task.notes = task_description
                employee_task.completed = task_completed

    db.session.commit()
    flash('Tâches enregistrées avec succès!', 'success')
    return redirect(url_for('task.add_task', date=selected_date.isoformat()))

# Route pour télécharger les pièces jointes
@task_bp.route('/download/<path:filename>')
@login_required
def download_file(filename):
    try:
        # Récupérer le dossier d'uploads depuis la configuration
        upload_folder = current_app.config['UPLOAD_FOLDER']
        print(f"Tentative de téléchargement du fichier: {filename} depuis {upload_folder}")

        # Vérifier si le fichier existe
        file_path = os.path.join(upload_folder, filename)
        if not os.path.exists(file_path):
            print(f"Le fichier n'existe pas: {file_path}")
            flash("Le fichier demandé n'existe pas.", "danger")
            return redirect(url_for('task.tasks'))

        # Renvoyer le fichier
        return send_from_directory(upload_folder, filename, as_attachment=True)
    except Exception as e:
        print(f"Erreur lors du téléchargement du fichier: {e}")
        flash(f"Erreur lors du téléchargement du fichier: {e}", "danger")
        return redirect(url_for('task.tasks'))

# Les blueprints sont importés et enregistrés directement dans app.py
