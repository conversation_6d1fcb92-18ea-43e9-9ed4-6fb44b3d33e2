from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
from sqlalchemy import <PERSON>um<PERSON>, Integer, String, Boolean, Float, Date, ForeignKey, Text, DateTime
from sqlalchemy.orm import relationship
from extensions import db

class User(UserMixin, db.Model):
    """Modèle pour les utilisateurs du système"""
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String(64), unique=True, nullable=False)
    email = Column(String(120), unique=True, nullable=False)
    password_hash = Column(String(128), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)

    # Permissions
    is_admin = Column(Boolean, default=False)
    can_manage_users = Column(Boolean, default=False)
    can_manage_employees = Column(Boolean, default=False)
    can_manage_tasks = Column(Boolean, default=False)
    can_manage_finances = Column(Boolean, default=False)
    can_access_registration_files = Column(Boolean, default=False)
    can_access_technical_files = Column(Boolean, default=False)
    can_access_reimbursement_files = Column(Boolean, default=False)
    can_access_organization_files = Column(Boolean, default=False)
    can_access_trainer_files = Column(Boolean, default=False)
    can_access_trainer_schedule = Column(Boolean, default=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'

class Employee(db.Model):
    """Modèle pour les employés"""
    __tablename__ = 'employees'

    id = Column(Integer, primary_key=True)
    first_name = Column(String(64), nullable=False)
    last_name = Column(String(64), nullable=False)
    position = Column(String(64), nullable=False)
    cin = Column(String(20), unique=True, nullable=False)
    cnss = Column(String(20), unique=True, nullable=True)
    phone = Column(String(20), nullable=True)
    email = Column(String(120), unique=True, nullable=True)
    daily_rate = Column(Float, nullable=False, default=0.0)  # Prix par jour en MAD
    hire_date = Column(Date, nullable=True)
    active = Column(Boolean, default=True)
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relations
    attendances = relationship('Attendance', back_populates='employee', cascade='all, delete-orphan')
    tasks = relationship('EmployeeTask', back_populates='employee', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Employee {self.first_name} {self.last_name}>'

    @property
    def full_name(self):
        return f'{self.first_name} {self.last_name}'

class Task(db.Model):
    """Modèle pour les tâches"""
    __tablename__ = 'tasks'

    id = Column(Integer, primary_key=True)
    title = Column(String(128), nullable=False)
    description = Column(Text, nullable=True)
    attachment_path = Column(String(255), nullable=True)  # Chemin vers la pièce jointe

    # Nouveaux champs
    priority = Column(Integer, default=0)  # 0=Basse, 1=Normale, 2=Haute, 3=Urgente
    status = Column(String(20), default='pending')  # pending, in_progress, completed, cancelled, delayed
    due_date = Column(Date, nullable=True)  # Date d'échéance
    category = Column(String(50), nullable=True)  # Catégorie de la tâche
    tags = Column(Text, nullable=True)  # Tags séparés par des virgules
    color = Column(String(20), default='#3498db')  # Couleur pour l'affichage
    assigned_to = Column(Integer, ForeignKey('users.id'), nullable=True)  # Utilisateur assigné

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relations
    employee_tasks = relationship('EmployeeTask', back_populates='task', cascade='all, delete-orphan')
    assigned_user = relationship('User', backref='assigned_tasks')

    @property
    def status_display(self):
        """Retourne le statut formaté pour l'affichage"""
        status_map = {
            'pending': 'En attente',
            'in_progress': 'En cours',
            'completed': 'Terminée',
            'cancelled': 'Annulée',
            'delayed': 'Retardée'
        }
        return status_map.get(self.status, self.status)

    @property
    def priority_display(self):
        """Retourne la priorité formatée pour l'affichage"""
        priority_map = {
            0: 'Basse',
            1: 'Normale',
            2: 'Haute',
            3: 'Urgente'
        }
        return priority_map.get(self.priority, 'Inconnue')

    @property
    def tag_list(self):
        """Retourne la liste des tags"""
        if not self.tags:
            return []
        return [tag.strip() for tag in self.tags.split(',')]

    def __repr__(self):
        return f'<Task {self.title}>'

class EmployeeTask(db.Model):
    """Modèle pour les tâches assignées aux employés"""
    __tablename__ = 'employee_tasks'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    task_id = Column(Integer, ForeignKey('tasks.id'), nullable=False)
    date = Column(Date, nullable=False, default=datetime.utcnow().date)  # Date d'assignation

    # Nouveaux champs
    start_date = Column(Date, nullable=True)  # Date de début prévue
    end_date = Column(Date, nullable=True)  # Date de fin prévue
    progress = Column(Integer, default=0)  # Progression en pourcentage (0-100)
    priority = Column(Integer, default=0)  # Priorité spécifique à l'assignation
    status = Column(String(20), default='pending')  # État spécifique à l'assignation

    completed = Column(Boolean, default=False)
    completion_date = Column(DateTime, nullable=True)
    notes = Column(Text, nullable=True)

    # Relations
    employee = relationship('Employee', back_populates='tasks')
    task = relationship('Task', back_populates='employee_tasks')

    @property
    def is_overdue(self):
        """Vérifie si la tâche est en retard"""
        if not self.end_date:
            return False
        return not self.completed and self.end_date < date.today()

    @property
    def days_remaining(self):
        """Calcule le nombre de jours restants avant la date d'échéance"""
        if not self.end_date:
            return None
        if self.completed:
            return 0
        delta = self.end_date - date.today()
        return delta.days

    @property
    def status_display(self):
        """Retourne le statut formaté pour l'affichage"""
        status_map = {
            'pending': 'En attente',
            'in_progress': 'En cours',
            'completed': 'Terminée',
            'cancelled': 'Annulée',
            'delayed': 'Retardée'
        }
        return status_map.get(self.status, self.status)

    def __repr__(self):
        return f'<EmployeeTask {self.employee.full_name} - {self.task.title}>'

class Attendance(db.Model):
    """Modèle pour les présences des employés"""
    __tablename__ = 'attendances'

    id = Column(Integer, primary_key=True)
    employee_id = Column(Integer, ForeignKey('employees.id'), nullable=False)
    date = Column(Date, nullable=False)
    present = Column(Boolean, default=True)
    arrival_time = Column(DateTime, nullable=True)
    departure_time = Column(DateTime, nullable=True)
    notes = Column(Text, nullable=True)

    # Relations
    employee = relationship('Employee', back_populates='attendances')

    def __repr__(self):
        status = "Présent" if self.present else "Absent"
        return f'<Attendance {self.employee.full_name} - {self.date} - {status}>'

class Transaction(db.Model):
    """Modèle pour les transactions financières (encaissements et décaissements)"""
    __tablename__ = 'transactions'

    id = Column(Integer, primary_key=True)
    date = Column(Date, nullable=False, default=datetime.utcnow().date)
    description = Column(Text, nullable=False)
    amount = Column(Float, nullable=False)  # Montant en MAD
    transaction_type = Column(String(20), nullable=False)  # 'encaissement' ou 'decaissement'
    created_by = Column(Integer, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relations
    user = relationship('User')

    def __repr__(self):
        return f'<Transaction {self.transaction_type} - {self.amount} MAD - {self.date}>'
