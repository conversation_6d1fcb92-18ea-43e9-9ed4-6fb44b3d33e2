{% extends "base.html" %}

{% block title %}Ajouter une tâche - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h1 class="mb-3"><i class="fas fa-plus me-2"></i>Ajouter une tâche</h1>
        <p class="text-muted">Créez une nouvelle tâche qui pourra être assignée aux employés.</p>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>Liste des tâches quotidiennes</h5>
                    </div>
                    <div class="col-md-6 text-end">
                        <button id="print-tasks" class="btn btn-info me-2">
                            <i class="fas fa-print me-1"></i> Imprimer
                        </button>
                        <form class="d-inline-flex" method="GET" action="{{ url_for('task.add_task') }}">
                            <input type="date" name="date" class="form-control me-2" value="{{ selected_date.isoformat() }}">
                            <button type="submit" class="btn btn-primary">Afficher</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- Formulaire unique pour toute la page -->
                <form method="POST" action="{{ url_for('task.save_task_checkboxes') }}" id="main-form" enctype="multipart/form-data">
                    <input type="hidden" name="date" value="{{ selected_date.isoformat() }}">

                    <!-- Section d'ajout de tâche intégrée en haut -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="title" class="form-label">Titre de la tâche</label>
                                            <input type="text" class="form-control" id="title" name="title" placeholder="Titre de la tâche">
                                        </div>

                                        <div class="col-md-3">
                                            <label for="employee_id" class="form-label">{{ form.employee_id.label }}</label>
                                            {{ form.employee_id(class="form-control") }}
                                            <small class="form-text text-muted">Sélectionnez un employé</small>
                                        </div>

                                        <div class="col-md-3">
                                            <label for="attachment" class="form-label">Pièce jointe</label>
                                            <input type="file" class="form-control" id="attachment" name="attachment">
                                            <small class="form-text text-muted">Formats: jpg, png, pdf, doc...</small>
                                        </div>

                                        <div class="col-md-2">
                                            <label for="description" class="form-label">Description</label>
                                            <textarea class="form-control" id="description" name="description" rows="1" placeholder="Description"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Liste des tâches quotidiennes -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> Cochez les tâches terminées et cliquez sur "Enregistrer" pour sauvegarder vos modifications.
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        {% for i in range(1, 51) %}
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="form-group">
                                                <label for="task_{{ i }}" class="form-label">Tâche {{ i }}</label>
                                                <input type="text" class="form-control" id="task_{{ i }}" name="task_{{ i }}" value="{{ tasks[i-1].description if i <= tasks|length else '' }}">
                                            </div>
                                        </div>
                                        <div class="col-md-4 d-flex align-items-end">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="completed_{{ i }}" name="completed_{{ i }}" {% if i <= tasks|length and tasks[i-1].completed %}checked{% endif %}>
                                                <label class="form-check-label" for="completed_{{ i }}">
                                                    Terminée
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{{ url_for('task.tasks') }}" class="btn btn-secondary me-2">Annuler</a>
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-1"></i> Enregistrer les tâches
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fonction pour imprimer la liste des tâches
        document.getElementById('print-tasks')?.addEventListener('click', function() {
            // Créer une fenêtre d'impression
            let printWindow = window.open('', '_blank');

            // Récupérer les tâches
            let tasks = [];
            for (let i = 1; i <= 50; i++) {
                let taskInput = document.getElementById('task_' + i);
                let completedCheckbox = document.getElementById('completed_' + i);

                if (taskInput && taskInput.value.trim() !== '') {
                    tasks.push({
                        number: i,
                        description: taskInput.value,
                        completed: completedCheckbox.checked
                    });
                }
            }

            // Contenu HTML à imprimer
            let content = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Liste des tâches quotidiennes - ${new Date().toLocaleDateString()}</title>
                    <style>
                        body { font-family: Arial, sans-serif; }
                        .header { text-align: center; margin-bottom: 20px; }
                        .task-table { width: 100%; border-collapse: collapse; }
                        .task-table th, .task-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        .task-table th { background-color: #f2f2f2; }
                        .completed { text-decoration: line-through; color: #999; }
                        .checkbox { width: 15px; height: 15px; border: 1px solid #000; display: inline-block; margin-right: 5px; }
                        .checkbox.checked { background-color: #000; }
                        .footer { margin-top: 30px; text-align: center; font-size: 12px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>Liste des tâches quotidiennes</h1>
                        <p>Date: ${new Date().toLocaleDateString()}</p>
                    </div>

                    <table class="task-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Description</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            // Ajouter les tâches
            tasks.forEach(task => {
                content += `
                    <tr>
                        <td>${task.number}</td>
                        <td class="${task.completed ? 'completed' : ''}">${task.description}</td>
                        <td>
                            <div class="checkbox ${task.completed ? 'checked' : ''}"></div>
                            ${task.completed ? 'Terminée' : 'En cours'}
                        </td>
                    </tr>
                `;
            });

            // Fermer le tableau et le document
            content += `
                        </tbody>
                    </table>

                    <div class="footer">
                        <p>Gestion des Pointages - Document généré automatiquement</p>
                    </div>
                </body>
                </html>
            `;

            // Écrire le contenu dans la fenêtre d'impression
            printWindow.document.open();
            printWindow.document.write(content);
            printWindow.document.close();

            // Imprimer après le chargement du contenu
            printWindow.onload = function() {
                printWindow.print();
            };
        });
    });
</script>
{% endblock %}
