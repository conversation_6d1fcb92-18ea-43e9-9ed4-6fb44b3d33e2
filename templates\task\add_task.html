{% extends "base.html" %}

{% block title %}Ajouter une tâche - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3"><i class="fas fa-plus me-2"></i>Ajouter une tâche</h1>
        <p class="text-muted">Créez une nouvelle tâche et définissez ses paramètres.</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('task.tasks') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Retour à la liste
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Informations de la tâche</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            {{ form.title.label(class="form-label") }}
                            {{ form.title(class="form-control") }}
                            {% if form.title.errors %}
                                <div class="text-danger">
                                    {% for error in form.title.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows="4") }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {% for error in form.description.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.status.label(class="form-label") }}
                            {{ form.status(class="form-select") }}
                            {% if form.status.errors %}
                                <div class="text-danger">
                                    {% for error in form.status.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.priority.label(class="form-label") }}
                            {{ form.priority(class="form-select") }}
                            {% if form.priority.errors %}
                                <div class="text-danger">
                                    {% for error in form.priority.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.due_date.label(class="form-label") }}
                            {{ form.due_date(class="form-control", type="date") }}
                            {% if form.due_date.errors %}
                                <div class="text-danger">
                                    {% for error in form.due_date.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.category.label(class="form-label") }}
                            {{ form.category(class="form-control") }}
                            {% if form.category.errors %}
                                <div class="text-danger">
                                    {% for error in form.category.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            {{ form.tags.label(class="form-label") }}
                            {{ form.tags(class="form-control") }}
                            <small class="form-text text-muted">Séparez les tags par des virgules (ex: urgent, important, projet)</small>
                            {% if form.tags.errors %}
                                <div class="text-danger">
                                    {% for error in form.tags.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if form.attachment %}
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            {{ form.attachment.label(class="form-label") }}
                            {{ form.attachment(class="form-control") }}
                            <small class="form-text text-muted">Formats acceptés: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG (Max: 10MB)</small>
                            {% if form.attachment.errors %}
                                <div class="text-danger">
                                    {% for error in form.attachment.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="row">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Enregistrer la tâche
                            </button>
                            <a href="{{ url_for('task.tasks') }}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-1"></i> Annuler
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Aide et conseils -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Conseils</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6><i class="fas fa-star text-warning me-1"></i>Titre</h6>
                    <small class="text-muted">Utilisez un titre clair et descriptif pour identifier facilement la tâche.</small>
                </div>
                
                <div class="mb-3">
                    <h6><i class="fas fa-flag text-info me-1"></i>Priorité</h6>
                    <small class="text-muted">
                        <strong>Urgente:</strong> À faire immédiatement<br>
                        <strong>Élevée:</strong> Important, à faire rapidement<br>
                        <strong>Normale:</strong> Priorité standard<br>
                        <strong>Faible:</strong> Peut attendre
                    </small>
                </div>
                
                <div class="mb-3">
                    <h6><i class="fas fa-calendar text-success me-1"></i>Date d'échéance</h6>
                    <small class="text-muted">Définissez une date limite réaliste pour la réalisation de la tâche.</small>
                </div>
                
                <div class="mb-3">
                    <h6><i class="fas fa-tags text-primary me-1"></i>Tags</h6>
                    <small class="text-muted">Utilisez des mots-clés pour organiser et retrouver facilement vos tâches.</small>
                </div>
            </div>
        </div>
        
        <!-- Statistiques rapides -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Statistiques</h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>Tâches totales:</span>
                    <span class="badge bg-primary">{{ total_tasks or 0 }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>En cours:</span>
                    <span class="badge bg-warning">{{ in_progress_tasks or 0 }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>Terminées:</span>
                    <span class="badge bg-success">{{ completed_tasks or 0 }}</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>En retard:</span>
                    <span class="badge bg-danger">{{ overdue_tasks or 0 }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus sur le champ titre
    document.getElementById('title').focus();
    
    // Validation côté client
    const form = document.querySelector('form');
    const titleInput = document.getElementById('title');
    
    form.addEventListener('submit', function(e) {
        if (titleInput.value.trim() === '') {
            e.preventDefault();
            titleInput.focus();
            titleInput.classList.add('is-invalid');
            
            // Supprimer la classe d'erreur après saisie
            titleInput.addEventListener('input', function() {
                titleInput.classList.remove('is-invalid');
            });
        }
    });
    
    // Prévisualisation des tags
    const tagsInput = document.getElementById('tags');
    if (tagsInput) {
        tagsInput.addEventListener('input', function() {
            const tags = this.value.split(',').map(tag => tag.trim()).filter(tag => tag);
            // Ici on pourrait ajouter une prévisualisation des tags
        });
    }
});
</script>
{% endblock %}
