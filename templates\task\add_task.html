{% extends "base.html" %}

{% block title %}Ajouter une tâche{% endblock %}

{% block styles %}
<style>
    :root {
        --primary-blue: #0073ea;
        --success-green: #00c875;
        --warning-orange: #ff9500;
        --danger-red: #e2445c;
        --gray-100: #f6f7fb;
        --gray-200: #e6e9ef;
        --gray-300: #c4c4c4;
        --gray-400: #676879;
        --gray-500: #323338;
        --white: #ffffff;
        --border-radius: 8px;
    }

    body {
        background-color: var(--gray-100);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .form-container {
        max-width: 800px;
        margin: 30px auto;
        background: var(--white);
        border-radius: var(--border-radius);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .form-header {
        background: var(--primary-blue);
        color: white;
        padding: 20px 30px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .form-header h2 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
    }

    .form-body {
        padding: 30px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: var(--gray-500);
        font-size: 14px;
    }

    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius);
        font-size: 14px;
        transition: border-color 0.2s;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-blue);
    }

    .form-select {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius);
        font-size: 14px;
        background: white;
        cursor: pointer;
    }

    .form-select:focus {
        outline: none;
        border-color: var(--primary-blue);
    }

    .row {
        display: flex;
        gap: 20px;
        margin: -10px;
    }

    .col {
        flex: 1;
        padding: 10px;
    }

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: var(--border-radius);
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }

    .btn-primary {
        background: var(--primary-blue);
        color: white;
    }

    .btn-primary:hover {
        background: #005bb5;
    }

    .btn-secondary {
        background: var(--gray-200);
        color: var(--gray-500);
    }

    .btn-secondary:hover {
        background: var(--gray-300);
    }

    .btn-success {
        background: var(--success-green);
        color: white;
    }

    .btn-success:hover {
        background: #00a866;
    }

    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        padding-top: 20px;
        border-top: 1px solid var(--gray-200);
        margin-top: 30px;
    }

    .file-upload {
        position: relative;
        display: inline-block;
        cursor: pointer;
        width: 100%;
    }

    .file-upload input[type=file] {
        position: absolute;
        left: -9999px;
    }

    .file-upload-label {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 15px;
        border: 2px dashed var(--gray-300);
        border-radius: var(--border-radius);
        background: var(--gray-100);
        color: var(--gray-400);
        cursor: pointer;
        transition: all 0.2s;
    }

    .file-upload-label:hover {
        border-color: var(--primary-blue);
        color: var(--primary-blue);
    }

    .priority-selector {
        display: flex;
        gap: 10px;
    }

    .priority-option {
        flex: 1;
        padding: 10px;
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius);
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 12px;
        font-weight: 600;
    }

    .priority-option.low {
        background: #e1f5fe;
        color: #0277bd;
    }

    .priority-option.medium {
        background: #fff3e0;
        color: #ef6c00;
    }

    .priority-option.high {
        background: #fce4ec;
        color: #c2185b;
    }

    .priority-option.urgent {
        background: #ffebee;
        color: #d32f2f;
    }

    .priority-option.selected {
        border-color: currentColor;
        transform: scale(1.05);
    }

    .status-selector {
        display: flex;
        gap: 10px;
    }

    .status-option {
        flex: 1;
        padding: 10px;
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius);
        text-align: center;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 12px;
        font-weight: 600;
    }

    .status-option.todo {
        background: var(--gray-200);
        color: var(--gray-500);
    }

    .status-option.working {
        background: var(--warning-orange);
        color: white;
    }

    .status-option.done {
        background: var(--success-green);
        color: white;
    }

    .status-option.stuck {
        background: var(--danger-red);
        color: white;
    }

    .status-option.selected {
        border-color: currentColor;
        transform: scale(1.05);
    }

    @media (max-width: 768px) {
        .form-container {
            margin: 15px;
        }
        
        .form-body {
            padding: 20px;
        }
        
        .row {
            flex-direction: column;
        }
        
        .priority-selector,
        .status-selector {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="form-container">
    <div class="form-header">
        <span style="font-size: 28px;">➕</span>
        <h2>Ajouter une nouvelle tâche</h2>
    </div>
    
    <div class="form-body">
        <form method="POST" enctype="multipart/form-data">
            {{ form.hidden_tag() }}
            
            <div class="form-group">
                <label class="form-label" for="title">Nom de la tâche *</label>
                {{ form.title(class="form-control", placeholder="Entrez le nom de la tâche...") }}
            </div>
            
            <div class="form-group">
                <label class="form-label" for="description">Description</label>
                {{ form.description(class="form-control", rows="4", placeholder="Décrivez la tâche en détail...") }}
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label class="form-label" for="assigned_to">Assigné à</label>
                        {{ form.assigned_to(class="form-select") }}
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label class="form-label" for="due_date">Date d'échéance</label>
                        {{ form.due_date(class="form-control") }}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label class="form-label">Priorité</label>
                        <div class="priority-selector">
                            <div class="priority-option low" data-priority="0">Faible</div>
                            <div class="priority-option medium selected" data-priority="1">Normale</div>
                            <div class="priority-option high" data-priority="2">Haute</div>
                            <div class="priority-option urgent" data-priority="3">Urgente</div>
                        </div>
                        {{ form.priority(style="display: none;") }}
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label class="form-label">Statut</label>
                        <div class="status-selector">
                            <div class="status-option todo selected" data-status="pending">À faire</div>
                            <div class="status-option working" data-status="in_progress">En cours</div>
                            <div class="status-option done" data-status="completed">Terminé</div>
                            <div class="status-option stuck" data-status="cancelled">Bloqué</div>
                        </div>
                        {{ form.status(style="display: none;") }}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label class="form-label" for="category">Catégorie</label>
                        {{ form.category(class="form-control", placeholder="Ex: Développement, Marketing...") }}
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label class="form-label" for="tags">Tags</label>
                        {{ form.tags(class="form-control", placeholder="Séparez par des virgules") }}
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="attachment">Fichier joint</label>
                <div class="file-upload">
                    {{ form.attachment(id="file-input") }}
                    <label for="file-input" class="file-upload-label">
                        <span>📎</span>
                        <span>Cliquez pour ajouter un fichier ou glissez-déposez</span>
                    </label>
                </div>
            </div>
            
            <div class="form-actions">
                <a href="{{ url_for('task.tasks') }}" class="btn btn-secondary">Annuler</a>
                <button type="submit" class="btn btn-success">💾 Enregistrer la tâche</button>
            </div>
        </form>
    </div>
</div>

<script>
// Gestion des sélecteurs de priorité
document.querySelectorAll('.priority-option').forEach(option => {
    option.addEventListener('click', function() {
        document.querySelectorAll('.priority-option').forEach(opt => opt.classList.remove('selected'));
        this.classList.add('selected');
        document.getElementById('priority').value = this.dataset.priority;
    });
});

// Gestion des sélecteurs de statut
document.querySelectorAll('.status-option').forEach(option => {
    option.addEventListener('click', function() {
        document.querySelectorAll('.status-option').forEach(opt => opt.classList.remove('selected'));
        this.classList.add('selected');
        document.getElementById('status').value = this.dataset.status;
    });
});

// Gestion du fichier upload
document.getElementById('file-input').addEventListener('change', function() {
    const label = document.querySelector('.file-upload-label span:last-child');
    if (this.files.length > 0) {
        label.textContent = `Fichier sélectionné: ${this.files[0].name}`;
    } else {
        label.textContent = 'Cliquez pour ajouter un fichier ou glissez-déposez';
    }
});

// Initialisation des valeurs par défaut
document.getElementById('priority').value = '1';
document.getElementById('status').value = 'pending';
</script>
{% endblock %}
