{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-database text-primary me-2"></i>
                        Gestion des Sauvegardes
                    </h3>
                    <div class="btn-group">
                        <a href="{{ url_for('company.create_backup') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Nouvelle Sauvegarde
                        </a>
                        <a href="{{ url_for('company.auto_backup_settings') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-cog me-1"></i> Paramètres Auto
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Statut de la sauvegarde automatique -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="alert {% if auto_settings.is_enabled %}alert-success{% else %}alert-warning{% endif %} d-flex align-items-center">
                                <i class="fas {% if auto_settings.is_enabled %}fa-check-circle{% else %}fa-exclamation-triangle{% endif %} me-2"></i>
                                <div class="flex-grow-1">
                                    <strong>Sauvegarde Automatique:</strong>
                                    {% if auto_settings.is_enabled %}
                                        Activée - {{ auto_settings.frequence|title }} à {{ auto_settings.heure_execution.strftime('%H:%M') }}
                                        {% if auto_settings.last_backup %}
                                            <br><small>Dernière sauvegarde: {{ auto_settings.last_backup.strftime('%d/%m/%Y à %H:%M') }}</small>
                                        {% endif %}
                                    {% else %}
                                        Désactivée
                                    {% endif %}
                                </div>
                                <a href="{{ url_for('company.auto_backup_settings') }}" class="btn btn-sm btn-outline-primary">
                                    Configurer
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Statistiques rapides -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4 class="mb-0">{{ backups|length }}</h4>
                                    <small>Total Sauvegardes</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4 class="mb-0">{{ backups|selectattr('type_sauvegarde', 'equalto', 'automatique')|list|length }}</h4>
                                    <small>Automatiques</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4 class="mb-0">{{ backups|selectattr('type_sauvegarde', 'equalto', 'manuel')|list|length }}</h4>
                                    <small>Manuelles</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h4 class="mb-0">
                                        {% set total_size = backups|sum(attribute='taille_fichier') %}
                                        {% if total_size %}
                                            {% if total_size < 1024 %}{{ "%.1f"|format(total_size) }} B
                                            {% elif total_size < 1048576 %}{{ "%.1f"|format(total_size/1024) }} KB
                                            {% elif total_size < 1073741824 %}{{ "%.1f"|format(total_size/1048576) }} MB
                                            {% else %}{{ "%.1f"|format(total_size/1073741824) }} GB
                                            {% endif %}
                                        {% else %}0 B{% endif %}
                                    </h4>
                                    <small>Espace Utilisé</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions rapides -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="btn-toolbar" role="toolbar">
                                <div class="btn-group me-2">
                                    <button type="button" class="btn btn-outline-primary" onclick="createBackupNow()">
                                        <i class="fas fa-save me-1"></i> Sauvegarde Rapide
                                    </button>
                                    <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#importModal">
                                        <i class="fas fa-upload me-1"></i> Importer Données
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#restoreModal">
                                        <i class="fas fa-undo me-1"></i> Restaurer
                                    </button>
                                </div>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-outline-danger" onclick="cleanOldBackups()">
                                        <i class="fas fa-trash me-1"></i> Nettoyer Anciennes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Liste des sauvegardes -->
                    {% if backups %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th width="5%">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th width="25%">Nom du Fichier</th>
                                    <th width="15%">Type</th>
                                    <th width="10%">Taille</th>
                                    <th width="15%">Date de Création</th>
                                    <th width="15%">Créé par</th>
                                    <th width="15%">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for backup in backups %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input backup-checkbox" value="{{ backup.id }}">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-file-archive text-primary me-2"></i>
                                            <div>
                                                <h6 class="mb-0">{{ backup.nom_fichier }}</h6>
                                                {% if backup.description %}
                                                <small class="text-muted">{{ backup.description }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge {% if backup.type_sauvegarde == 'automatique' %}bg-success{% else %}bg-primary{% endif %}">
                                            {{ backup.type_sauvegarde|title }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if backup.taille_fichier %}
                                            {% if backup.taille_fichier < 1024 %}{{ "%.1f"|format(backup.taille_fichier) }} B
                                            {% elif backup.taille_fichier < 1048576 %}{{ "%.1f"|format(backup.taille_fichier/1024) }} KB
                                            {% elif backup.taille_fichier < 1073741824 %}{{ "%.1f"|format(backup.taille_fichier/1048576) }} MB
                                            {% else %}{{ "%.1f"|format(backup.taille_fichier/1073741824) }} GB
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">Inconnue</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span title="{{ backup.created_at.strftime('%d/%m/%Y à %H:%M:%S') }}">
                                            {{ backup.created_at.strftime('%d/%m/%Y') }}
                                            <br><small class="text-muted">{{ backup.created_at.strftime('%H:%M') }}</small>
                                        </span>
                                    </td>
                                    <td>
                                        {% if backup.creator %}
                                            {{ backup.creator.username }}
                                        {% else %}
                                            <span class="text-muted">Système</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('company.download_backup', backup_id=backup.id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="Télécharger">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                    title="Informations" onclick="showBackupInfo({{ backup.id }})">
                                                <i class="fas fa-info"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    title="Restaurer" onclick="confirmRestore({{ backup.id }}, '{{ backup.nom_fichier }}')">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    title="Supprimer" onclick="confirmDelete({{ backup.id }}, '{{ backup.nom_fichier }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-database fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune sauvegarde trouvée</h5>
                        <p class="text-muted">Commencez par créer votre première sauvegarde.</p>
                        <a href="{{ url_for('company.create_backup') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Créer une Sauvegarde
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la sauvegarde <strong id="backup-name"></strong> ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form method="POST" id="delete-form" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'importation -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Importer des Données</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="import-form" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label class="form-label">Fichier à importer</label>
                        <input type="file" class="form-control" name="import_file" accept=".csv,.xlsx,.xls,.json" required>
                        <small class="form-text text-muted">Formats supportés: CSV, Excel, JSON</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Table de destination</label>
                        <select class="form-select" name="table_name" required>
                            <option value="">Sélectionner une table...</option>
                            <option value="users">Utilisateurs</option>
                            <option value="employees">Employés</option>
                            <option value="simple_tasks">Tâches</option>
                            <option value="attendance">Présences</option>
                            <option value="financial_records">Enregistrements financiers</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mode d'importation</label>
                        <select class="form-select" name="mode_import">
                            <option value="append">Ajouter aux données existantes</option>
                            <option value="replace">Remplacer les données existantes</option>
                            <option value="update">Mettre à jour les données existantes</option>
                        </select>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="premiere_ligne_entetes" checked>
                        <label class="form-check-label">
                            La première ligne contient les en-têtes
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="submitImport()">Importer</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de restauration -->
<div class="modal fade" id="restoreModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Restaurer une Sauvegarde</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Attention!</strong> La restauration remplacera toutes les données actuelles.
                </div>
                <form id="restore-form" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label class="form-label">Fichier de sauvegarde</label>
                        <input type="file" class="form-control" name="backup_file" accept=".sql,.zip,.gz" required>
                        <small class="form-text text-muted">Formats supportés: SQL, ZIP, GZ</small>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="confirm_restore" required>
                        <label class="form-check-label">
                            Je confirme vouloir restaurer cette sauvegarde (ATTENTION: cela remplacera toutes les données actuelles)
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-danger" onclick="submitRestore()">Restaurer</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// Sélection multiple
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.backup-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Confirmation de suppression
function confirmDelete(backupId, backupName) {
    document.getElementById('backup-name').textContent = backupName;
    document.getElementById('delete-form').action = "{{ url_for('company.delete_backup', backup_id=0) }}".replace('0', backupId);
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Sauvegarde rapide
function createBackupNow() {
    if (confirm('Créer une sauvegarde maintenant ?')) {
        window.location.href = "{{ url_for('company.create_backup') }}";
    }
}

// Nettoyer anciennes sauvegardes
function cleanOldBackups() {
    if (confirm('Supprimer les sauvegardes de plus de 30 jours ?')) {
        // Implémenter la logique de nettoyage
        alert('Fonctionnalité en cours de développement');
    }
}

// Soumettre l'importation
function submitImport() {
    const form = document.getElementById('import-form');
    const formData = new FormData(form);
    
    // Ici, vous pouvez ajouter la logique d'importation via AJAX
    alert('Fonctionnalité d\'importation en cours de développement');
}

// Soumettre la restauration
function submitRestore() {
    const form = document.getElementById('restore-form');
    const formData = new FormData(form);
    
    if (confirm('Êtes-vous absolument sûr de vouloir restaurer cette sauvegarde ? Toutes les données actuelles seront perdues.')) {
        // Ici, vous pouvez ajouter la logique de restauration via AJAX
        alert('Fonctionnalité de restauration en cours de développement');
    }
}
</script>
{% endblock %}
