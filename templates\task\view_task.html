{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-tasks text-primary me-2"></i>
                        {{ task.title }}
                    </h3>
                    <div class="btn-group">
                        <a href="{{ url_for('task.edit_task', id=task.id) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit"></i> Modifier
                        </a>
                        <a href="{{ url_for('task.print_task', id=task.id) }}" class="btn btn-info btn-sm" target="_blank">
                            <i class="fas fa-print"></i> Imprimer
                        </a>
                        <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="fas fa-trash"></i> Supprimer
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="task-details">
                                <h5 class="mb-3">Description</h5>
                                <div class="description-box p-3 bg-light rounded">
                                    {% if task.description %}
                                        {{ task.description|nl2br|safe }}
                                    {% else %}
                                        <em class="text-muted">Aucune description fournie</em>
                                    {% endif %}
                                </div>

                                {% if task.attachment_path %}
                                <div class="mt-4">
                                    <h5 class="mb-3">Pièce jointe</h5>
                                    <div class="attachment-box p-3 bg-light rounded">
                                        <i class="fas fa-paperclip text-primary me-2"></i>
                                        <a href="{{ url_for('static', filename='uploads/' + task.attachment_path) }}" 
                                           target="_blank" class="text-decoration-none">
                                            {{ task.attachment_path }}
                                        </a>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="task-info">
                                <h5 class="mb-3">Informations</h5>
                                <div class="info-item mb-3">
                                    <strong>Statut:</strong>
                                    <span class="badge 
                                        {% if task.status == 'nouveau' %}bg-primary
                                        {% elif task.status == 'en_cours' %}bg-warning
                                        {% elif task.status == 'termine' %}bg-success
                                        {% elif task.status == 'annule' %}bg-danger
                                        {% endif %} ms-2">
                                        {% if task.status == 'nouveau' %}Nouveau
                                        {% elif task.status == 'en_cours' %}En cours
                                        {% elif task.status == 'termine' %}Terminé
                                        {% elif task.status == 'annule' %}Annulé
                                        {% endif %}
                                    </span>
                                </div>
                                
                                <div class="info-item mb-3">
                                    <strong>Priorité:</strong>
                                    <span class="badge 
                                        {% if task.priority == 'basse' %}bg-secondary
                                        {% elif task.priority == 'normale' %}bg-info
                                        {% elif task.priority == 'haute' %}bg-warning
                                        {% elif task.priority == 'urgente' %}bg-danger
                                        {% endif %} ms-2">
                                        {% if task.priority == 'basse' %}Basse
                                        {% elif task.priority == 'normale' %}Normale
                                        {% elif task.priority == 'haute' %}Haute
                                        {% elif task.priority == 'urgente' %}Urgente
                                        {% endif %}
                                    </span>
                                </div>

                                {% if task.due_date %}
                                <div class="info-item mb-3">
                                    <strong>Date d'échéance:</strong>
                                    <div class="mt-1">
                                        <i class="fas fa-calendar text-primary me-2"></i>
                                        {{ task.due_date.strftime('%d/%m/%Y') }}
                                    </div>
                                </div>
                                {% endif %}

                                {% if task.category %}
                                <div class="info-item mb-3">
                                    <strong>Catégorie:</strong>
                                    <div class="mt-1">
                                        <i class="fas fa-folder text-primary me-2"></i>
                                        {{ task.category }}
                                    </div>
                                </div>
                                {% endif %}

                                {% if task.tags %}
                                <div class="info-item mb-3">
                                    <strong>Tags:</strong>
                                    <div class="mt-1">
                                        {% for tag in task.tags.split(',') %}
                                            <span class="badge bg-light text-dark me-1">#{{ tag.strip() }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                <div class="info-item mb-3">
                                    <strong>Créé le:</strong>
                                    <div class="mt-1">
                                        <i class="fas fa-clock text-primary me-2"></i>
                                        {{ task.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                    </div>
                                </div>

                                {% if task.updated_at and task.updated_at != task.created_at %}
                                <div class="info-item mb-3">
                                    <strong>Modifié le:</strong>
                                    <div class="mt-1">
                                        <i class="fas fa-edit text-primary me-2"></i>
                                        {{ task.updated_at.strftime('%d/%m/%Y à %H:%M') }}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('task.tasks') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour à la liste
                        </a>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success btn-sm" onclick="updateTaskStatus('termine')">
                                <i class="fas fa-check"></i> Marquer comme terminé
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" onclick="updateTaskStatus('en_cours')">
                                <i class="fas fa-play"></i> En cours
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir supprimer cette tâche ? Cette action est irréversible.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <a href="{{ url_for('task.delete_task', id=task.id) }}" class="btn btn-danger">Supprimer</a>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block styles %}
<style>
.description-box {
    min-height: 100px;
    white-space: pre-wrap;
}

.info-item {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.info-item:last-child {
    border-bottom: none;
}

.attachment-box {
    border: 1px dashed #ddd;
}

.task-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

@media print {
    .btn, .modal, .card-footer {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
function updateTaskStatus(status) {
    // إنشاء نموذج مخفي لتحديث حالة المهمة
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ url_for("task.edit_task", id=task.id) }}';
    
    // إضافة الحقول المخفية
    const fields = {
        'title': '{{ task.title }}',
        'description': '{{ task.description or "" }}',
        'due_date': '{{ task.due_date.strftime("%Y-%m-%d") if task.due_date else "" }}',
        'category': '{{ task.category or "" }}',
        'tags': '{{ task.tags or "" }}',
        'priority': '{{ task.priority }}',
        'status': status,
        'submit': '1'
    };
    
    for (const [name, value] of Object.entries(fields)) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = name;
        input.value = value;
        form.appendChild(input);
    }
    
    document.body.appendChild(form);
    form.submit();
}
</script>
{% endblock %}
