{% extends "base.html" %}

{% block title %}{{ task.title }} - Gestion des Pointages{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="mb-3"><i class="fas fa-eye me-2"></i>{{ task.title }}</h1>
        <p class="text-muted">Détails complets de la tâche #{{ task.id }}.</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('task.tasks') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Retour à la liste
        </a>
        <a href="{{ url_for('task.edit_task', id=task.id) }}" class="btn btn-warning">
            <i class="fas fa-edit me-1"></i> Modifier
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Informations principales -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations principales</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-3"><strong>Titre:</strong></div>
                    <div class="col-md-9">{{ task.title }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-3"><strong>Description:</strong></div>
                    <div class="col-md-9">
                        {% if task.description %}
                            <div style="white-space: pre-wrap;">{{ task.description }}</div>
                        {% else %}
                            <span class="text-muted">Aucune description</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-3"><strong>Statut:</strong></div>
                    <div class="col-md-9">
                        {% if task.status == 'new' %}
                            <span class="badge bg-secondary fs-6">Nouvelle</span>
                        {% elif task.status == 'in_progress' %}
                            <span class="badge bg-warning fs-6">En cours</span>
                        {% elif task.status == 'completed' %}
                            <span class="badge bg-success fs-6">Terminée</span>
                        {% elif task.status == 'cancelled' %}
                            <span class="badge bg-danger fs-6">Annulée</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-3"><strong>Priorité:</strong></div>
                    <div class="col-md-9">
                        {% if task.priority == 'low' %}
                            <span class="badge bg-light text-dark fs-6">Faible</span>
                        {% elif task.priority == 'normal' %}
                            <span class="badge bg-info fs-6">Normale</span>
                        {% elif task.priority == 'high' %}
                            <span class="badge bg-warning fs-6">Élevée</span>
                        {% elif task.priority == 'urgent' %}
                            <span class="badge bg-danger fs-6">Urgente</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-3"><strong>Date d'échéance:</strong></div>
                    <div class="col-md-9">
                        {% if task.due_date %}
                            {{ task.due_date.strftime('%d/%m/%Y') }}
                            {% if task.is_overdue %}
                                <span class="badge bg-danger ms-2">En retard</span>
                            {% endif %}
                        {% else %}
                            <span class="text-muted">Non définie</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-3"><strong>Catégorie:</strong></div>
                    <div class="col-md-9">
                        {% if task.category %}
                            <span class="badge bg-primary">{{ task.category }}</span>
                        {% else %}
                            <span class="text-muted">Aucune catégorie</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-3"><strong>Tags:</strong></div>
                    <div class="col-md-9">
                        {% if task.tags %}
                            {% for tag in task.tags.split(',') %}
                                <span class="badge bg-secondary me-1">{{ tag.strip() }}</span>
                            {% endfor %}
                        {% else %}
                            <span class="text-muted">Aucun tag</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Fichier joint -->
        {% if task.attachment_path %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-paperclip me-2"></i>Fichier joint</h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <i class="fas fa-file fa-2x text-primary me-3"></i>
                    <div>
                        <h6 class="mb-1">{{ task.attachment_path.split('/')[-1] }}</h6>
                        <small class="text-muted">Ajouté le {{ task.created_at.strftime('%d/%m/%Y à %H:%M') if task.created_at else 'Date inconnue' }}</small>
                    </div>
                    <div class="ms-auto">
                        <a href="{{ url_for('task.download_file', filename=task.attachment_path.split('/')[-1]) }}" class="btn btn-primary">
                            <i class="fas fa-download me-1"></i> Télécharger
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Historique -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Historique</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Tâche créée</h6>
                            <small class="text-muted">{{ task.created_at.strftime('%d/%m/%Y à %H:%M') if task.created_at else 'Date inconnue' }}</small>
                        </div>
                    </div>
                    
                    {% if task.updated_at and task.updated_at != task.created_at %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Dernière modification</h6>
                            <small class="text-muted">{{ task.updated_at.strftime('%d/%m/%Y à %H:%M') }}</small>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if task.status == 'completed' %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Tâche terminée</h6>
                            <small class="text-muted">Statut: Terminée</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('task.edit_task', id=task.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i> Modifier
                    </a>
                    
                    {% if task.status != 'completed' %}
                    <button type="button" class="btn btn-success" onclick="markAsCompleted()">
                        <i class="fas fa-check me-1"></i> Marquer comme terminée
                    </button>
                    {% endif %}
                    
                    {% if task.status != 'in_progress' and task.status != 'completed' %}
                    <button type="button" class="btn btn-info" onclick="markAsInProgress()">
                        <i class="fas fa-play me-1"></i> Marquer en cours
                    </button>
                    {% endif %}
                    
                    <a href="{{ url_for('task.print_task', id=task.id) }}" class="btn btn-secondary" target="_blank">
                        <i class="fas fa-print me-1"></i> Imprimer
                    </a>
                    
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                        <i class="fas fa-trash me-1"></i> Supprimer
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Métadonnées -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info me-2"></i>Métadonnées</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>ID:</strong><br>
                    <span class="text-muted">#{{ task.id }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Créée le:</strong><br>
                    <span class="text-muted">{{ task.created_at.strftime('%d/%m/%Y à %H:%M') if task.created_at else 'Date inconnue' }}</span>
                </div>
                
                <div class="mb-3">
                    <strong>Modifiée le:</strong><br>
                    <span class="text-muted">{{ task.updated_at.strftime('%d/%m/%Y à %H:%M') if task.updated_at else 'Jamais modifiée' }}</span>
                </div>
                
                {% if task.due_date %}
                <div class="mb-3">
                    <strong>Échéance:</strong><br>
                    <span class="text-muted">{{ task.due_date.strftime('%d/%m/%Y') }}</span>
                    {% if task.is_overdue %}
                        <br><small class="text-danger">⚠️ En retard</small>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la tâche <strong>"{{ task.title }}"</strong> ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <a href="{{ url_for('task.delete_task', id=task.id) }}" class="btn btn-danger">Supprimer</a>
            </div>
        </div>
    </div>
</div>

<!-- Formulaire caché pour les actions rapides -->
<form id="quick-action-form" method="POST" action="{{ url_for('task.edit_task', id=task.id) }}" style="display: none;">
    {{ csrf_token() }}
    <input type="hidden" name="title" value="{{ task.title }}">
    <input type="hidden" name="description" value="{{ task.description or '' }}">
    <input type="hidden" name="due_date" value="{{ task.due_date.strftime('%Y-%m-%d') if task.due_date else '' }}">
    <input type="hidden" name="category" value="{{ task.category or '' }}">
    <input type="hidden" name="tags" value="{{ task.tags or '' }}">
    <input type="hidden" name="priority" value="{{ task.priority }}">
    <input type="hidden" name="status" id="quick-status" value="{{ task.status }}">
</form>
{% endblock %}

{% block styles %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}
</style>
{% endblock %}

{% block scripts %}
<script>
function markAsCompleted() {
    document.getElementById('quick-status').value = 'completed';
    document.getElementById('quick-action-form').submit();
}

function markAsInProgress() {
    document.getElementById('quick-status').value = 'in_progress';
    document.getElementById('quick-action-form').submit();
}

function confirmDelete() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
