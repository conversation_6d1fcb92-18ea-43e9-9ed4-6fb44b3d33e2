"""
Modèles pour les informations de l'entreprise
"""
from extensions import db
from datetime import datetime

class CompanyInfo(db.Model):
    """Modèle pour les informations de l'entreprise"""
    __tablename__ = 'company_info'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Informations de base
    nom_entreprise = db.Column(db.String(200), nullable=False)
    raison_sociale = db.Column(db.String(200))
    secteur_activite = db.Column(db.String(100))
    forme_juridique = db.Column(db.String(50))
    
    # Informations de contact
    adresse = db.Column(db.Text)
    ville = db.Column(db.String(100))
    code_postal = db.Column(db.String(20))
    pays = db.Column(db.String(50), default='Maroc')
    telephone = db.Column(db.String(20))
    fax = db.Column(db.String(20))
    email = db.Column(db.String(120))
    site_web = db.Column(db.String(200))
    
    # Informations légales
    numero_registre_commerce = db.Column(db.String(50))
    numero_ice = db.Column(db.String(50))
    numero_if = db.Column(db.String(50))
    numero_cnss = db.Column(db.String(50))
    numero_patente = db.Column(db.String(50))
    
    # Capital et banque
    capital_social = db.Column(db.Float)
    devise_capital = db.Column(db.String(10), default='MAD')
    banque_principale = db.Column(db.String(100))
    numero_compte_bancaire = db.Column(db.String(50))
    rib = db.Column(db.String(50))
    
    # Fichiers et logos
    logo_path = db.Column(db.String(200))
    signature_path = db.Column(db.String(200))
    cachet_path = db.Column(db.String(200))
    
    # Pied de page pour impressions
    pied_de_page = db.Column(db.Text)
    mentions_legales = db.Column(db.Text)
    
    # Métadonnées
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    def __repr__(self):
        return f'<CompanyInfo {self.nom_entreprise}>'
    
    @classmethod
    def get_active_company(cls):
        """Récupère les informations de l'entreprise active"""
        return cls.query.filter_by(is_active=True).first()
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire"""
        return {
            'id': self.id,
            'nom_entreprise': self.nom_entreprise,
            'raison_sociale': self.raison_sociale,
            'secteur_activite': self.secteur_activite,
            'forme_juridique': self.forme_juridique,
            'adresse': self.adresse,
            'ville': self.ville,
            'code_postal': self.code_postal,
            'pays': self.pays,
            'telephone': self.telephone,
            'fax': self.fax,
            'email': self.email,
            'site_web': self.site_web,
            'numero_registre_commerce': self.numero_registre_commerce,
            'numero_ice': self.numero_ice,
            'numero_if': self.numero_if,
            'numero_cnss': self.numero_cnss,
            'numero_patente': self.numero_patente,
            'capital_social': self.capital_social,
            'devise_capital': self.devise_capital,
            'banque_principale': self.banque_principale,
            'numero_compte_bancaire': self.numero_compte_bancaire,
            'rib': self.rib,
            'logo_path': self.logo_path,
            'signature_path': self.signature_path,
            'cachet_path': self.cachet_path,
            'pied_de_page': self.pied_de_page,
            'mentions_legales': self.mentions_legales,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'is_active': self.is_active
        }

class DatabaseBackup(db.Model):
    """Modèle pour la gestion des sauvegardes de base de données"""
    __tablename__ = 'database_backups'
    
    id = db.Column(db.Integer, primary_key=True)
    nom_fichier = db.Column(db.String(200), nullable=False)
    chemin_fichier = db.Column(db.String(500), nullable=False)
    taille_fichier = db.Column(db.BigInteger)  # en bytes
    type_sauvegarde = db.Column(db.String(20), default='manuel')  # manuel, automatique
    description = db.Column(db.Text)
    
    # Métadonnées
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # Relations
    creator = db.relationship('User', backref='backups_created')
    
    def __repr__(self):
        return f'<DatabaseBackup {self.nom_fichier}>'
    
    def get_file_size_formatted(self):
        """Retourne la taille du fichier formatée"""
        if not self.taille_fichier:
            return "Inconnue"
        
        # Convertir en unités lisibles
        for unit in ['B', 'KB', 'MB', 'GB']:
            if self.taille_fichier < 1024.0:
                return f"{self.taille_fichier:.1f} {unit}"
            self.taille_fichier /= 1024.0
        return f"{self.taille_fichier:.1f} TB"

class AutoBackupSettings(db.Model):
    """Modèle pour les paramètres de sauvegarde automatique"""
    __tablename__ = 'auto_backup_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Paramètres de fréquence
    is_enabled = db.Column(db.Boolean, default=False)
    frequence = db.Column(db.String(20), default='daily')  # daily, weekly, monthly
    heure_execution = db.Column(db.Time, default=datetime.strptime('02:00', '%H:%M').time())
    jour_semaine = db.Column(db.Integer)  # 0=Lundi, 6=Dimanche (pour weekly)
    jour_mois = db.Column(db.Integer)  # 1-31 (pour monthly)
    
    # Paramètres de rétention
    nombre_max_sauvegardes = db.Column(db.Integer, default=30)
    supprimer_anciennes = db.Column(db.Boolean, default=True)
    
    # Paramètres de stockage
    dossier_sauvegarde = db.Column(db.String(500))
    inclure_uploads = db.Column(db.Boolean, default=True)
    compression = db.Column(db.Boolean, default=True)
    
    # Notifications
    email_notification = db.Column(db.Boolean, default=False)
    email_destinataire = db.Column(db.String(120))
    
    # Métadonnées
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_backup = db.Column(db.DateTime)
    
    def __repr__(self):
        return f'<AutoBackupSettings {self.frequence}>'
    
    @classmethod
    def get_settings(cls):
        """Récupère les paramètres de sauvegarde automatique"""
        settings = cls.query.first()
        if not settings:
            # Créer des paramètres par défaut
            settings = cls()
            db.session.add(settings)
            db.session.commit()
        return settings
