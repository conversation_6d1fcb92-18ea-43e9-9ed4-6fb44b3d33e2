"""
Migration pour ajouter le champ assigned_to à la table tasks
"""

from extensions import db
from sqlalchemy import text

def upgrade():
    """Ajouter le champ assigned_to à la table tasks"""
    try:
        # Vérifier si la colonne existe déjà
        result = db.session.execute(text("PRAGMA table_info(tasks)"))
        columns = [row[1] for row in result.fetchall()]
        
        if 'assigned_to' not in columns:
            # Ajouter la colonne assigned_to
            db.session.execute(text("ALTER TABLE tasks ADD COLUMN assigned_to INTEGER"))
            print("✅ Colonne 'assigned_to' ajoutée à la table 'tasks'")
        else:
            print("ℹ️ Colonne 'assigned_to' existe déjà dans la table 'tasks'")
            
        db.session.commit()
        
    except Exception as e:
        print(f"❌ Erreur lors de l'ajout de la colonne assigned_to: {e}")
        db.session.rollback()
        raise

def downgrade():
    """Supprimer le champ assigned_to de la table tasks"""
    try:
        # SQLite ne supporte pas DROP COLUMN directement
        # Il faut recréer la table sans la colonne
        print("⚠️ SQLite ne supporte pas la suppression de colonnes directement")
        print("Pour supprimer la colonne, il faudrait recréer la table")
        
    except Exception as e:
        print(f"❌ Erreur lors de la suppression de la colonne assigned_to: {e}")
        db.session.rollback()
        raise

if __name__ == "__main__":
    # Exécuter la migration
    upgrade()
